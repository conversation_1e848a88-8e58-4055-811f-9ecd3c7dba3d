import os
import argparse
import numpy as np

import torch
from torch.utils.data import DataLoader
from datasets.RD_dataset_3d import RD_dataset_3d

from utils import create_model,create_confusion_matrix, evaluate_2d, create_classifier,create_datasets, set_seed, evaluate_3d

RANDOM_SEED = 233
set_seed(RANDOM_SEED)

def main(args):

    is_3d = args.is_3d
    print('is_3d:', is_3d)
    collate_fn = None
    if is_3d:
        evaluate = evaluate_3d
        collate_fn = RD_dataset_3d.collate_fn
    else:
        evaluate = evaluate_2d
        collate_fn = None

    device = torch.device(args.device if torch.cuda.is_available() else "cpu")

    train_set, test_set=create_datasets('RD_dataset')
    print('test_set len:',len(test_set))

    batch_size = args.batch_size
    nw = min([os.cpu_count(), batch_size if batch_size > 1 else 0, 8])

    test_loader=DataLoader(test_set,shuffle=False,batch_size=batch_size,num_workers=nw)

    out_features = args.out_features
    model = create_model(args.model_name,out_features=args.out_features)
    classifier = create_classifier(num_classes=args.num_classes,in_features=out_features)

    model = model.to(device)
    classifier = classifier.to(device) 

    if args.weights != "":
        assert os.path.exists(args.weights), "weights file: '{}' not exist.".format(args.weights)
        weights_dict = torch.load(args.weights, map_location=device)
        print(model.load_state_dict(weights_dict['network'], strict=False))
        print(classifier.load_state_dict(weights_dict['classifier'], strict=False))


    val_acc, all_labels, all_preds = evaluate(model=model,
                                              data_loader=test_loader,
                                              device=device,
                                              epoch=0,
                                              classifier=classifier)

    create_confusion_matrix(0,all_labels,all_preds,args.num_classes)





if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--num_classes', type=int, default=4)
    parser.add_argument('--batch_size', type=int, default=16)

    parser.add_argument('--model_name', default='backbone', help='create models name')
    parser.add_argument('--out_features', type=int, default=512, help='output feature size of backbone')

    parser.add_argument('--weights', type=str, default=r".\weights\backbone-0.7696087352138308-4.pth",
                        help='initial weights path')

    parser.add_argument('--device', default='cuda:0', help='device id (i.e. 0 or 0,1 or cpu)')
    parser.add_argument('--is_3d', type=bool, default=False, help='is 3d dataset')

    opt = parser.parse_args()

    main(opt)
