import os
import numpy as np
import torch
from PIL import Image
from torch import nn
from torch.utils.data import Dataset
import torchvision.transforms as transforms
import torch.nn.functional as F

def complex_to_db_image(data):
    complex_data = data[0] + 1j * data[1]

    magnitude = np.abs(complex_data) # (T/None, H, W)

    eps = 1e-6
    magnitude = 20 * np.log10(magnitude + eps)

    magnitude -= magnitude.min()
    magnitude /= (magnitude.max() + eps)
    magnitude *= 255.0
    magnitude = magnitude.astype(np.uint8)

    return magnitude

def complex_to_abs_angle(data):
    complex_data = data[0] + 1j * data[1]
    magnitude = np.abs(complex_data)  
    angle = np.angle(complex_data)
    data = np.stack([magnitude, angle], axis=0) # [2, T/None, H, W]
    # if magnitude.ndim == 3:
    #     data = magnitude[None, :, :, :]
    # else:
    #     data = magnitude[None, :, :]
    return data

def extract_number(file_name):
    return int(file_name.split('_')[0])

# RD_new
class RD_dataset_3d(Dataset):
    def __init__(self, folder_list, transform=None):
        self.folder_list = folder_list
        # -----------img---------------------------
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize((128, 334)),
                transforms.ToTensor()
            ])
        else:
            self.transform = transform
        # -----------img--------------------------


    def get_label(self, folder_path):
        folder_name = os.path.basename(folder_path)

        label = int(folder_name.split('_')[-1]) - 1
        return label

    def __len__(self):
        return len(self.folder_list)

    def __getitem__(self, idx):

        folder_path = self.folder_list[idx]
        label = self.get_label(folder_path)
        frames = []
        for file_name in sorted(os.listdir(folder_path), key=extract_number):
            file_path = os.path.join(folder_path, file_name)
            data = np.load(file_path)  # [2, H, W]
            data = complex_to_db_image(data)  # [H, W]
            img = Image.fromarray(data).convert('RGB') #[W, H, 3]
            img = self.transform(img) # [3, H, W]
            frames.append(img)

        video = torch.stack(frames, dim=0) # [T, 3, H, W]

        label = torch.tensor(label, dtype=torch.long)

        return video, label

    @staticmethod
    def collate_fn(batch):
        video_seqs, labels = zip(*batch)
        lengths = [v.shape[0] for v in video_seqs]
        padded_videos = nn.utils.rnn.pad_sequence(video_seqs, batch_first=True)  # [B, T_max, C, H, W]
        padded_videos = padded_videos.permute(0, 2, 1, 3, 4) # [B, C, T_max, H, W]
        labels = torch.tensor(labels)
        return padded_videos, lengths, labels


