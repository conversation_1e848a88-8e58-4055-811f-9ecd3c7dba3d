import torch
import torch.nn as nn
import torch.nn.functional as F
from functools import partial
import timm
from timm.models.vision_transformer import Block
from timm.layers import trunc_normal_, DropPath


class Range_PatchEmbed(nn.Module):

    def __init__(self, seq_len=31, feature_dim=512, embed_dim=768):
        super().__init__()
        self.seq_len = seq_len
        self.feature_dim = feature_dim
        self.embed_dim = embed_dim
        
        self.proj = nn.Linear(feature_dim, embed_dim)
        
        self.norm = nn.LayerNorm(embed_dim)
        
    def forward(self, x):

        B, C, T, D = x.shape
        assert C == 1 and T == self.seq_len and D == self.feature_dim, \
            f"Expected input shape (B, 1, {self.seq_len}, {self.feature_dim}), got {x.shape}"
        
        x = x.squeeze(1)
        
        x = self.proj(x)
        x = self.norm(x)
        
        return x


class Range_SequenceTransformer(nn.Module):

    def __init__(self, 
                 seq_len=31,
                 feature_dim=512,
                 embed_dim=768,
                 depth=12,
                 num_heads=12,
                 mlp_ratio=4.0,
                 qkv_bias=True,
                 drop_rate=0.0,
                 attn_drop_rate=0.0,
                 drop_path_rate=0.1,
                 norm_layer=None,
                 use_cls_token=True,
                 global_pool=False,
                 out_features=None):
        super().__init__()
        
        norm_layer = norm_layer or partial(nn.LayerNorm, eps=1e-6)
        self.embed_dim = embed_dim
        self.use_cls_token = use_cls_token
        self.global_pool = global_pool
        self.num_tokens = 1 if use_cls_token else 0
        
        self.patch_embed = Range_PatchEmbed(
            seq_len=seq_len, 
            feature_dim=feature_dim, 
            embed_dim=embed_dim
        )

        if use_cls_token:
            self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        
        self.pos_embed = nn.Parameter(torch.zeros(1, seq_len + self.num_tokens, embed_dim))
        self.pos_drop = nn.Dropout(p=drop_rate)
        
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, depth)]

        self.blocks = nn.ModuleList([
            Block(
                dim=embed_dim,
                num_heads=num_heads,
                mlp_ratio=mlp_ratio,
                qkv_bias=qkv_bias,
                attn_drop=attn_drop_rate,
                drop_path=dpr[i],
                norm_layer=norm_layer
            ) for i in range(depth)
        ])
        
        self.norm = norm_layer(embed_dim)
        
        if out_features is not None and out_features != embed_dim:
            self.head = nn.Linear(embed_dim, out_features)
        else:
            self.head = nn.Identity()
            
        if global_pool:
            self.fc_norm = norm_layer(embed_dim)
        
        self.init_weights()
    
    def init_weights(self):
        
        trunc_normal_(self.pos_embed, std=0.02)
        
        if self.use_cls_token:
            trunc_normal_(self.cls_token, std=0.02)
        
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
    
    def forward_features(self, x):

        x = self.patch_embed(x)
        B, T, D = x.shape
        
        if self.use_cls_token:
            cls_tokens = self.cls_token.expand(B, -1, -1)  # (B, 1, embed_dim)
            x = torch.cat((cls_tokens, x), dim=1)  # (B, 32, embed_dim)
        
        x = x + self.pos_embed
        x = self.pos_drop(x)
        
        for blk in self.blocks:
            x = blk(x)
        
        x = self.norm(x)
        
        return x
    
    def forward(self, x):

        x = self.forward_features(x)
        
        if self.global_pool:
            if self.use_cls_token:
                x = x[:, 1:, :].mean(dim=1)  # (B, embed_dim)
            else:
                x = x.mean(dim=1)  # (B, embed_dim)
            x = self.fc_norm(x)
        elif self.use_cls_token:
            x = x[:, 0]  # (B, embed_dim)
        
        x = self.head(x)
        
        return x


def create_range_sequence_transformer_tiny(**kwargs):

    model = Range_SequenceTransformer(
        embed_dim=192,
        depth=12,
        num_heads=3,
        mlp_ratio=4,
        **kwargs
    )
    return model


def create_range_sequence_transformer_small(**kwargs):

    model = Range_SequenceTransformer(
        embed_dim=384,
        depth=12,
        num_heads=8,
        mlp_ratio=4,
        **kwargs
    )
    return model


def create_range_sequence_transformer_base(**kwargs):

    model = Range_SequenceTransformer(
        embed_dim=768,
        depth=12,
        num_heads=12,
        mlp_ratio=4,
        **kwargs
    )
    return model


def create_range_sequence_transformer_large(**kwargs):

    model = Range_SequenceTransformer(
        embed_dim=1024,
        depth=24,
        num_heads=16,
        mlp_ratio=4,
        **kwargs
    )
    return model

def create_range_sequence_transformer(**kwargs):

    return create_range_sequence_transformer_base(**kwargs)
