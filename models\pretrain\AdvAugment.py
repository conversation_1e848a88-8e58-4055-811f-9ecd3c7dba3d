import torch
import torch.nn as nn
import torch.nn.functional as F

class Data_augment(nn.Module):
    def __init__(self, img_noise=False, img_horizontal_flip=False, img_random_mask=False):
        super(Data_augment, self).__init__()

        self.img_noise = img_noise
        self.img_noise_std = 0.5

        self.img_horizontal_flip = img_horizontal_flip

        self.img_random_mask = img_random_mask
        self.img_mask_ratio = 0.1


    def Add_Image_Noise(self, x):
        noise = torch.normal(mean=0, std=self.img_noise_std, size=x.shape, device=x.device)
        x_noisy = torch.clamp(x + noise, 0, 1)
        return x_noisy

    def Image_Horizontal_Flip(self, x):
        batch_size = x.shape[0]
        for i in range(batch_size):
            if torch.rand(1).item() > 0.5:
                x[i] = torch.flip(x[i], dims=[2])
        return x

    def Image_Random_Mask(self, x):
        batch_size, _, _, width = x.shape

        for i in range(batch_size):
            mask_width = int(width * self.img_mask_ratio)

            start_pos = torch.randint(0, width - mask_width + 1, (1,)).item()
            end_pos = start_pos + mask_width

            x[i, :, :, start_pos:end_pos] = 0

        return x



    def forward(self, x):
        if not x.is_cuda:
            x = x.cuda()

        if self.img_noise:
            x = self.Add_Image_Noise(x)

        if self.img_horizontal_flip:
            x = self.Image_Horizontal_Flip(x)

        if self.img_random_mask:
            x = self.Image_Random_Mask(x)

        return x