import argparse
import json
import numpy as np
import os
import random

import torch
from colorama import Fore
from torch.utils.data import DataLoader

import timm

assert timm.__version__ == "0.3.2"  # version check
from timm.layers import trunc_normal_
from timm.data.mixup import Mixup
from timm.loss import LabelSmoothingCrossEntropy, SoftTargetCrossEntropy


from models.pretrain.mae.pos_embed import interpolate_pos_embed

from utils import param_groups_lrd, create_datasets, finetune_mae, evaluate_2d, create_classifier, create_model, set_seed

RANDOM_SEED = 233
set_seed(RANDOM_SEED)
evaluate = evaluate_2d

def main(args):

    device = torch.device(args.device)

    if not os.path.exists("./weights"):
        os.makedirs(args.output_dir)

    train_set, test_set = create_datasets(args.dataset_name)
    print('train set size:', len(train_set))
    print('test set size:', len(test_set))

    train_loader = DataLoader(train_set,
                              shuffle=True,
                              batch_size=args.batch_size,
                              num_workers=args.num_workers,
                              pin_memory=args.pin_mem,
                              drop_last=True)

    test_loader = DataLoader(test_set,
                             shuffle=True,
                             batch_size=args.batch_size,
                             num_workers=args.num_workers,
                             pin_memory=args.pin_mem,
                             drop_last=False)

    # mixup
    #-------------------------------------------------------------
    mixup_fn = None
    mixup_active = args.mixup > 0 or args.cutmix > 0. or args.cutmix_minmax is not None
    if mixup_active:
        print("Mixup is activated!")
        mixup_fn = Mixup(
            mixup_alpha=args.mixup, cutmix_alpha=args.cutmix, cutmix_minmax=args.cutmix_minmax,
            prob=args.mixup_prob, switch_prob=args.mixup_switch_prob, mode=args.mixup_mode,
            label_smoothing=args.smoothing, num_classes=args.num_classes)
    #-------------------------------------------------------------

    model = create_model(args.model_name, drop_path_rate=args.drop_path, global_pool=args.global_pool)
    classifier = create_classifier(in_features=args.out_features, num_classes=args.num_classes)
    print('model created:', args.model_name)
    print('global pool:', args.global_pool)
    print('num_classes:', args.num_classes)

    if args.pretrain_weights:
        checkpoint = torch.load(args.pretrain_weights, map_location='cpu')
        checkpoint_model = checkpoint

        # for k in ['head.weight', 'head.bias']:
        #     print(f"Removing key {k} from pretrained checkpoint")
        #     del checkpoint_model[k]
        interpolate_pos_embed(model, checkpoint_model)

        # load pre-trained model
        msg = model.load_state_dict(checkpoint_model, strict=False)
        print(msg)

        # manually initialize classifier layer
        trunc_normal_(classifier.linear.weight, std=2e-5)

    model.to(device)
    classifier.to(device)

    n_parameters = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print('number of params (M): %.2f' % (n_parameters / 1.e6))

    eff_batch_size = args.batch_size * args.accum_iter

    if args.lr is None:  # only base_lr is specified
        args.lr = args.blr * eff_batch_size / 256

    print("base lr: %.2e" % (args.lr * 256 / eff_batch_size))
    print("actual lr: %.2e" % args.lr)

    print("accumulate grad iterations: %d" % args.accum_iter)
    print("effective batch size: %d" % eff_batch_size)

    # build optimizer with layer-wise lr decay (lrd)
    param_groups = param_groups_lrd(model, args.weight_decay,
                                    no_weight_decay_list=model.no_weight_decay(),
                                    layer_decay=args.layer_decay)
    param_groups.append({"params": classifier.parameters(), "lr": args.lr})
    optimizer = torch.optim.AdamW(param_groups, lr=args.lr)

    if mixup_fn is not None:
        criterion = SoftTargetCrossEntropy()
    elif args.smoothing > 0.:
        criterion = LabelSmoothingCrossEntropy(smoothing=args.smoothing)
    else:
        criterion = torch.nn.CrossEntropyLoss()
    print("criterion = %s" % str(criterion))


    print(f"warmup epochs: {args.warmup_epochs}")
    best_acc = 0
    val_acc_list = []
    for epoch in range(args.epochs):
        # train
        finetune_mae(model=model,
                     classifier=classifier,
                     criterion=criterion,
                     optimizer=optimizer,
                     data_loader=train_loader,
                     device=device,
                     epoch=epoch,
                     args=args)

        # validate
        val_acc, all_labels, all_preds = evaluate(model=model,
                                                  data_loader=test_loader,
                                                  device=device,
                                                  epoch=epoch,
                                                  classifier=classifier)
        val_acc_list.append(val_acc)
        # create_confusion_matrix(epoch, all_labels, all_preds, args.num_classes)

        if val_acc >= best_acc:
            best_acc = val_acc
            print(Fore.WHITE + "Best Acc:{:.2f}".format(best_acc * 100))
            if epoch >= args.save_epoch:
                torch.save({"network": model.state_dict(),
                            "classifier": classifier.state_dict()},
                           "./weights/{}-{}.pth".format(args.model_name, val_acc))

    print("Best Acc: {:.2f}".format(best_acc * 100))

if __name__ == '__main__':
    parser = argparse.ArgumentParser('MAE fine-tuning for image classification', add_help=False)
    parser.add_argument('--batch_size', default=64, type=int)
    parser.add_argument('--epochs', default=50, type=int)
    parser.add_argument('--accum_iter', default=1, type=int,
                        help='Accumulate gradient iterations (for increasing the effective batch size under memory constraints)')
    parser.add_argument('--warmup_epochs', type=int, default=5, help='epochs to warmup LR')

    # model
    parser.add_argument('--model_name', default='vit_large_patch16', type=str, help='vit_base_patch16, vit_large_patch16, vit_huge_patch14')
    parser.add_argument('--input_size', default=224, type=int,)
    parser.add_argument('--drop_path', type=float, default=0.1)

    # Optimizer
    parser.add_argument('--clip_grad', type=float, default=None, metavar='NORM',
                        help='Clip gradient norm (default: None, no clipping)')
    parser.add_argument('--weight_decay', type=float, default=0.05,
                        help='weight decay (default: 0.05)')
    parser.add_argument('--lr', type=float, default=None, metavar='LR',
                        help='learning rate (absolute lr)')
    parser.add_argument('--blr', type=float, default=1e-3, metavar='LR',
                        help='base learning rate: absolute_lr = base_lr * total_batch_size / 256')
    parser.add_argument('--min_lr', type=float, default=1e-6, metavar='LR',
                        help='lower lr bound for cyclic schedulers that hit 0')
    parser.add_argument('--layer_decay', type=float, default=0.75,
                        help='layer-wise lr decay from ELECTRA/BEiT')

    parser.add_argument('--smoothing', type=float, default=0.1, help='Label smoothing (default: 0.1)')

    # * Mixup params·
    parser.add_argument('--mixup', type=float, default=0,
                        help='mixup alpha, mixup enabled if > 0.')
    parser.add_argument('--cutmix', type=float, default=0,
                        help='cutmix alpha, cutmix enabled if > 0.')
    parser.add_argument('--cutmix_minmax', type=float, nargs='+', default=None,
                        help='cutmix min/max ratio, overrides alpha and enables cutmix if set (default: None)')
    parser.add_argument('--mixup_prob', type=float, default=1.0,
                        help='Probability of performing mixup or cutmix when either/both is enabled')
    parser.add_argument('--mixup_switch_prob', type=float, default=0.5,
                        help='Probability of switching to cutmix when both mixup and cutmix enabled')
    parser.add_argument('--mixup_mode', type=str, default='batch',
                        help='How to apply mixup/cutmix params. Per "batch", "pair", or "elem"')

    # * Finetuning params
    parser.add_argument('--pretrain_weights', default='', help='finetune from checkpoint')
    parser.add_argument('--global_pool', action='store_true')
    parser.set_defaults(global_pool=True)
    parser.add_argument('--cls_token', action='store_false', dest='global_pool',
                        help='Use class token instead of global pool for classification')

    # Dataset parameters
    parser.add_argument('--dataset_name', default='RD_dataset_3d', type=str, help='dataset name')
    parser.add_argument('--num_classes', default=4, type=int)
    parser.add_argument('--device', default='cuda:0')

    parser.add_argument('--num_workers', default=10, type=int)
    parser.add_argument('--pin_mem', action='store_true',
                        help='Pin CPU memory in DataLoader for more efficient (sometimes) transfer to GPU.')
    parser.add_argument('--no_pin_mem', action='store_false', dest='pin_mem')
    parser.set_defaults(pin_mem=True)

    parser.add_argument('--out_features', type=int, default=768, help='output feature size of backbone')

    args = parser.parse_args()
    main(args)