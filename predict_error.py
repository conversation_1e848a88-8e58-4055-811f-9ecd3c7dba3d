import os
import argparse
import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from tqdm import tqdm

from utils import create_model, create_classifier, create_datasets, set_seed

RANDOM_SEED = 233
set_seed(RANDOM_SEED)

def extract_sample_info(file_path):
    
    folder_path = os.path.dirname(file_path)
    folder_name = os.path.basename(folder_path)
    file_name = os.path.basename(file_path)

    folder_path_0 = os.path.dirname(folder_path)
    folder_name_0 = os.path.basename(folder_path_0)
    # frame_num = int(file_name.split('.')[0])
    # frame_num = int(file_name.split('_')[0])

    return f"{folder_name}_{file_name}"

@torch.no_grad()
def predict_and_save_errors(model, classifier, data_loader, device, output_file, args, fold):

    model.eval()
    classifier.eval()

    error_samples = []
    total_samples = 0
    correct_samples = 0

    for batch_idx, (images, labels, videos) in enumerate(tqdm(data_loader, desc=f"Fold {fold} predict progress")):
        images = images.to(device)
        labels = labels.to(device)

        batch_start_idx = batch_idx * args.batch_size # [0 batch_size...]

        features = model(images)
        logits = classifier(features)
        probabilities = F.softmax(logits, dim=1)

        confidences, predicted = torch.max(probabilities, dim=1)

        for i in range(images.shape[0]):
            sample_idx = batch_start_idx + i
            if sample_idx >= len(data_loader.dataset):
                break

            true_label = labels[i].item()
            pred_label = predicted[i].item()
            confidence = confidences[i].item()

            file_path = data_loader.dataset.file_paths[sample_idx]
            sample_name = extract_sample_info(file_path)

            total_samples += 1

            if pred_label != true_label:
                error_info = {
                    'sample_name': sample_name,
                    'true_label': true_label,
                    'predicted_label': pred_label,
                    'confidence': confidence
                }
                error_samples.append(error_info)
            else:
                correct_samples += 1

    accuracy = correct_samples / total_samples if total_samples > 0 else 0
    print(f"\nFold {fold} predict done!")
    print(f"total samples: {total_samples}")
    print(f"correct samples: {correct_samples}")
    print(f"error samples: {len(error_samples)}")
    print(f"accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")

    with open(output_file, 'w', encoding='utf-8') as f:
        if error_samples:
            f.write(f"{'name':<55} {'True':<10} {'predict':<10} {'score':<10} \n")
            f.write("\n")
            for error in error_samples:
                f.write(f"{error['sample_name']:<55} "
                       f"{error['true_label']:<10} "
                       f"{error['predicted_label']:<10} "
                       f"{error['confidence']:<10.4f} \n")
        else:
            f.write("no error samples\n")

    print(f"error samples info saved to: {output_file}")
    return error_samples, accuracy

def main(args):

    device = torch.device(args.device if torch.cuda.is_available() else "cpu")

    if args.root != '':
        root = args.root
        root_type = root.split('/')[-1]
    else:
        root = None
        root_type = None

    datasets_list = create_datasets('RD_dataset_2d', root=root)

    model_name = 'backbone'

    all_error_samples = []
    all_accuracies = []

    for _, test_set, fold in datasets_list:
        print(f'\nFold {fold}: test_set={len(test_set)}')

        if len(test_set) > 0:
            sample_path = test_set.file_paths[0]
            sample_info = extract_sample_info(sample_path)
            print(f"sample example: {sample_info} (path: {sample_path})")

        test_loader = DataLoader(
            test_set,
            batch_size=args.batch_size,
            shuffle=False,
            num_workers=0)

        model = create_model(model_name, name=args.backbone_name)
        out_features = model.out_features
        classifier = create_classifier(num_classes=args.num_classes, in_features=out_features)
        print("Model created:", model_name, args.backbone_name)

        model = model.to(device)
        classifier = classifier.to(device)

        if root_type:
            weights_path = f"./weights/{root_type}-2d-{fold}.pth"
        else:
            weights_path = f"./weights/backbone-2d-{fold}.pth"

        if os.path.exists(weights_path):
            print(f"load weights: {weights_path}")
            weights_dict = torch.load(weights_path, map_location=device)

            model_load_info = model.load_state_dict(weights_dict['network'], strict=False)
            classifier_load_info = classifier.load_state_dict(weights_dict['classifier'], strict=False)

            print("model weights load info:", model_load_info)
            print("classifier weights load info:", classifier_load_info)
        else:
            print(f"warning: weights file not found: {weights_path}, use random initialized model")

        if root_type:
            output_file = f"error_{root_type}_{fold}.txt"
        else:
            output_file = f"error_backbone_{fold}.txt"

        error_samples, accuracy = predict_and_save_errors(
            model, classifier, test_loader, device, output_file, args, fold
        )

        all_error_samples.extend(error_samples)
        all_accuracies.append(accuracy)

    if all_accuracies:
        avg_accuracy = sum(all_accuracies) / len(all_accuracies)
        print(f"Total folds: {len(all_accuracies)}")
        print(f"Individual accuracies: {[f'{acc*100:.2f}%' for acc in all_accuracies]}")
        print(f"Average accuracy: {avg_accuracy*100:.2f}%")
        print(f"Total error samples across all folds: {len(all_error_samples)}")

    print(f"\nAll predictions done!")

if __name__ == '__main__':
    parser = argparse.ArgumentParser()

    parser.add_argument('--num_classes', type=int, default=4, help='number of classes')
    parser.add_argument('--batch_size', type=int, default=32, help='batch size')
    parser.add_argument('--backbone_name', default='resnet', help='backbone name')
    parser.add_argument('--root', default='./data/SNR_RD_npy', help='root path')
    parser.add_argument('--device', default='cuda:0', help='device (cuda:0 or cpu)')

    args = parser.parse_args()

    print(f"num_classes: {args.num_classes}")
    print(f"batch_size: {args.batch_size}")
    print(f"backbone_name: {args.backbone_name}")
    print(f"root: {args.root}")
    print(f"device: {args.device}")

    main(args)
