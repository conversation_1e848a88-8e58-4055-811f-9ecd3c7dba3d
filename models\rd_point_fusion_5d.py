import torch
from torch import nn
import torch.nn.functional as F
from einops import rearrange
import math
from backbone_transformer_4d import Backbone_Transformer_4D

class network(nn.Module):
    def __init__(self,layer_number=6,sequence_length=30,dim=512,heads=6,dim_head=128,class_num=6,dropout=0.):
        super(network, self).__init__()
        self.Backbone_Transformer_4D=Backbone_Transformer_4D(num_heads=4, depth=3, mlp_ratio=4, drop_rate=0.1, max_seq_len=30, embed_dim=392,
                 lstm_hidden_dim=256, lstm_layers=2, dropout=0.1, name='resnet') # this is for the spatial embedding
        self.Decoder=nn.Sequential()
        self.layer_numebr=layer_number
        self.sequence_length=sequence_length
        self.encode_position=PositionalEncoding(dim)
        self.point_track=point_track_embedding(dim)
        self.point_track_block=nn.ModuleList()
        for i in range(layer_number):
            self.Decoder.append(Decode_block(dim,heads,dim_head))
            self.point_track_block.append(Point_track_block(dim,heads,dim_head))
        # self.fc_out=nn.Linear(dim,class_num)


    def forward(self, rd, T_lengths, n_lengths, point_data, track_data, return_intermediate=False):
        """

        :param x:  [B, T, N, C, H, W]
        :return:
        """
        src_mask=self.src_padding(rd, T_lengths)
        tr_mask=self.tar_mask_get(src_mask)
        if return_intermediate:
            rd, intermediate_rd = self.Backbone_Transformer_4D(rd, T_lengths, n_lengths, return_intermediate=True) # B T Dim (512)
        else:
            rd=self.Backbone_Transformer_4D(rd, T_lengths, n_lengths) # B T Dim (512)
        rd=self.encode_position(rd)

        point_data,track_data=self.point_track(point_data,track_data)

        for i in range(self.layer_numebr):
            x=self.Decoder[i](rd, src_mask, tr_mask, point_data, track_data)
            point_data,track_data=self.point_track_block[i](point_data,track_data,tr_mask)

        one_hot=x+point_data+track_data
        # x=self.fc_out(x+point_data+track_data)
        
        if return_intermediate:
            return one_hot, intermediate_rd
        else:
            return one_hot

    def tar_mask_get(self,src_mask):
        trg_len = self.sequence_length
        trg_sub_mask = torch.tril(torch.ones(trg_len, trg_len)).type(torch.ByteTensor).to(src_mask.device)
        trg_mask = src_mask & trg_sub_mask # (batch_size,1,seq_len,seq_len)
        return trg_mask

    def src_padding(self,x:torch.Tensor,t_length):
        """

        :param x: B C T_{i}
        :return: x B T_length C
        """
        src_mask=torch.ones(size=(x.shape[0],self.sequence_length),device=x.device)
        for i in range(x.shape[0]):
            src_mask[i,self.sequence_length-t_length[i]]=1.
        src_mask=(src_mask!=0.).unsqueeze(1).unsqueeze(2) #b 1 1 T_length
        return src_mask

class Decode_block(nn.Module):
    def __init__(self,dim,heads,dim_head,dropout=0.):
        super(Decode_block, self).__init__()
        self.norm1=nn.LayerNorm(dim)
        self.norm2=nn.LayerNorm(dim)
        self.norm3=nn.LayerNorm(dim)
        self.norm4=nn.LayerNorm(dim)
        self.norm_point=nn.LayerNorm(dim)
        self.norm_track=nn.LayerNorm(dim)
        self.attention1=MHSA(dim,heads,dim_head)
        self.ff1=FeedForward(dim,dim*4)
        self.attention2=MHSA(dim,heads,dim_head)
        self.ff2=FeedForward(dim,dim*4)

    def forward(self,x,src_mask,tr_mask,point_data,track_data):
        point_data=self.norm_point(point_data)
        track_data=self.norm_track(track_data)
        x=self.attention1(self.norm1(x),point_data,track_data,tr_mask)+x
        x=self.ff1(self.norm2(x),tr_mask)+x
        x=self.attention2(self.norm3(x),point_data,track_data,tr_mask)+x
        x=self.ff2(self.norm4(x),tr_mask)+x
        return x

class MHSA(nn.Module):
    def __init__(self,dim,heads,dim_head,dropout=0.):
        super().__init__()
        inner_dim=heads*dim_head
        project_out = not (heads == 1 and dim_head == dim)
        self.heads=heads
        self.scale=dim_head**-0.5
        self.attend = nn.Softmax(dim=-1)
        self.to_qkv = nn.Linear(dim, inner_dim * 3, bias=False)

        self.to_kv_point=nn.Linear(dim, inner_dim * 2, bias=False)
        self.to_kv_track = nn.Linear(dim, inner_dim * 2, bias=False)

        self.to_out = nn.Sequential(
            nn.Linear(inner_dim, dim),
            nn.Dropout(dropout),
        ) if project_out else nn.Identity()



    def forward(self,x,point_data,track_data,mask=None):
        b, n, _, h = *x.shape, self.heads
        qkv = self.to_qkv(x).chunk(3, dim=-1)  # (b, n(65), dim*3) ---> 3 * (b, n, dim)
        q, k, v = map(lambda t: rearrange(t, 'b n (h d) -> b h n d', h=h), qkv)  # q, k, v   (b, h, n, dim_head(64))

        point_kv=self.to_kv_point(point_data).chunk(2,dim=-1)
        point_k,point_v=map(lambda t: rearrange(t, 'b n (h d) -> b h n d', h=h), point_kv)

        track_kv=self.to_kv_track(track_data).chunk(2,dim=-1)
        track_k,track_v=map(lambda t: rearrange(t, 'b n (h d) -> b h n d', h=h), track_kv)

        dots = torch.einsum('b h i d, b h j d -> b h i j', q, k) * self.scale
        point_dots=torch.einsum('b h i d, b h j d -> b h i j', q, point_k) * self.scale
        track_dots=torch.einsum('b h i d, b h j d -> b h i j', q, track_k) * self.scale
        dots=dots+point_dots+track_dots
        v=v+point_v+track_v
        if mask is not None:
            dots=dots.masked_fill(mask==0,-1e9)

        attn = self.attend(dots)

        out = torch.einsum('b h i j, b h j d -> b h i d', attn, v)
        out = rearrange(out, 'b h n d -> b n (h d)')
        return self.to_out(out)

class FeedForward(nn.Module):
    def __init__(self, dim, hidden_dim, dropout=0.):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(dim, hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, dim),
            nn.Dropout(dropout)
        )
    def forward(self, x,mask):
        return self.net(x)


class PositionalEncoding(nn.Module):
     def __init__(self, d_model, max_len=5000):
         super(PositionalEncoding,self).__init__()
         position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1) # (max_len, 1)
         div_term = torch.exp(torch.arange(0, d_model, 2).float() * -(math.log(10000.0) / d_model))
         pe = torch.zeros(1, max_len, d_model)   # (1, max_len, d_model)
         pe[0, :, 0::2] = torch.sin(position * div_term)    # even index
         pe[0, :, 1::2] = torch.cos(position * div_term)    # odd index
         self.register_buffer('pe', pe)
     def forward(self, x):
         # x: (batch_size, seq_len, d_model)
         x = x + self.pe[:, :x.size(1), :]     # add positional encoding to input tensor
         return x

class original_MHSA(nn.Module):
    def __init__(self, dim, heads, dim_head, dropout=0.):
        super(original_MHSA,self).__init__()
        inner_dim = heads * dim_head
        project_out = not (heads == 1 and dim_head == dim)
        self.heads = heads
        self.scale = dim_head ** -0.5
        self.attend = nn.Softmax(dim=-1)
        self.to_qkv = nn.Linear(dim, inner_dim * 3, bias=False)

        self.to_out = nn.Sequential(
            nn.Linear(inner_dim, dim),
            nn.Dropout(dropout),
        ) if project_out else nn.Identity()

    def forward(self, x, mask=None):
        b, n, _, h = *x.shape, self.heads
        qkv = self.to_qkv(x).chunk(3, dim=-1)  # (b, n(65), dim*3) ---> 3 * (b, n, dim)
        q, k, v = map(lambda t: rearrange(t, 'b n (h d) -> b h n d', h=h), qkv)  # q, k, v   (b, h, n, dim_head(64))

        dots = torch.einsum('b h i d, b h j d -> b h i j', q, k) * self.scale

        if mask is not None:
            dots = dots.masked_fill(mask == 0, -1e9)

        attn = self.attend(dots)

        out = torch.einsum('b h i j, b h j d -> b h i d', attn, v)
        out = rearrange(out, 'b h n d -> b n (h d)')
        return self.to_out(out)

class point_track_embedding(nn.Module):
    def __init__(self,dim):
        super(point_track_embedding, self).__init__()
        self.point_embedding=nn.Sequential(nn.Linear(7,dim),
                                           nn.LayerNorm(dim),
                                           nn.GELU(),
                                           nn.Linear(dim,dim),
                                           nn.LayerNorm(dim),
                                           nn.GELU(),
                                           nn.Linear(dim,dim))
        self.track_embedding=nn.Sequential(nn.Linear(8,dim),
                                           nn.LayerNorm(dim),
                                           nn.GELU(),
                                           nn.Linear(dim,dim),
                                           nn.LayerNorm(dim),
                                           nn.GELU(),
                                           nn.Linear(dim,dim))
        self.point_positon=PositionalEncoding(dim)
        self.track_position=PositionalEncoding(dim)


    def forward(self,point_data,track_data):
        """

        :param point_data: B T 7
        :param track_data: B T 8
        :return:
        """
        point_data=self.point_embedding(point_data)
        track_data=self.track_embedding(track_data)

        point_data=self.point_positon(point_data)
        track_data=self.track_position(track_data)
        return point_data,track_data

class Point_track_block(nn.Module):
    def __init__(self,dim,heads,dim_head):
        super(Point_track_block, self).__init__()
        self.point_feature=nn.Sequential(nn.Linear(dim,dim*2),
                                         nn.LayerNorm(dim*2),
                                         nn.GELU(),
                                         nn.Linear(dim*2,dim*2),
                                         nn.LayerNorm(dim*2),
                                         nn.GELU(),
                                         nn.Linear(dim*2,dim)
                                         )
        self.point_weight=nn.Sequential(nn.Linear(30,dim*2),
                                        nn.LayerNorm(dim*2),
                                        nn.GELU(),
                                        nn.Linear(dim*2, dim * 2),
                                        nn.LayerNorm(dim * 2),
                                        nn.GELU(),
                                        nn.Linear(dim*2,30)
                                        )
        self.point_MHSA=original_MHSA(dim,heads,dim_head)
        self.norm1=nn.LayerNorm(dim)
        self.norm3=nn.LayerNorm(dim)


        self.track_feature=nn.Sequential(nn.Linear(dim,dim*2),
                                         nn.LayerNorm(dim*2),
                                         nn.GELU(),
                                         nn.Linear(dim*2,dim*2),
                                         nn.LayerNorm(dim*2),
                                         nn.GELU(),
                                         nn.Linear(dim*2,dim))

        self.track_weight=nn.Sequential(nn.Linear(30,dim*2),
                                        nn.LayerNorm(dim*2),
                                        nn.GELU(),
                                        nn.Linear(dim*2, dim * 2),
                                        nn.LayerNorm(dim * 2),
                                        nn.GELU(),
                                        nn.Linear(dim*2,30)
                                        )
        self.track_MHSA=original_MHSA(dim,heads,dim_head)
        self.norm2=nn.LayerNorm(dim)
        self.norm4=nn.LayerNorm(dim)
        self.pool=nn.AdaptiveAvgPool1d(output_size=1)

    def forward(self,point_data,track_data,tr_mask):
        """

        :param point_data: B T D
        :param track_data: B T D
        :return:
        """
        point_weight=self.point_weight(self.pool(point_data).squeeze())
        point_data=self.point_MHSA(self.norm1(point_data),tr_mask)*F.sigmoid(point_weight[...,None])+point_data
        point_data=self.point_feature(self.norm3(point_data))*F.sigmoid(point_weight[...,None])+point_data

        track_weight=self.track_weight(self.pool(track_data).squeeze())
        track_data=self.track_MHSA(self.norm2(track_data),tr_mask)*F.sigmoid(track_weight[...,None])+track_data
        track_data=self.track_feature(self.norm4(track_data))*F.sigmoid(track_weight[...,None])+track_data

        return point_data,track_data

def create_rd_point_fusion_5d(**kwargs):
    
    return network(**kwargs)