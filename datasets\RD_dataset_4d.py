import os
import numpy as np
import torch
from PIL import Image
from torch import nn
from torch.utils.data import Dataset
import torchvision.transforms as transforms
import torch.nn.functional as F

def complex_to_db_image(data):
    complex_data = data[0] + 1j * data[1]

    magnitude = np.abs(complex_data) # (H, W)

    eps = 1e-6
    magnitude = 20 * np.log10(magnitude + eps)

    magnitude -= magnitude.min()
    magnitude /= (magnitude.max() + eps)
    magnitude *= 255.0
    magnitude = magnitude.astype(np.uint8)

    return magnitude

def extract_number_first(file_name):
    return int(file_name.split('_')[0])

def extract_number_last(file_name):
    return int(file_name.split('_')[-1].split('.')[0])

class RD_dataset_4d(Dataset):
    def __init__(self, folder_list, transform=None):
        self.trails_list = folder_list
        # -----------img---------------------------
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize((128, 334)),
                transforms.ToTensor()
            ])
        else:
            self.transform = transform
        # -----------img--------------------------

    def get_label(self, trail_path):
        folder_name = os.path.basename(trail_path)
        label = int(folder_name.split('_')[-1]) - 1
        return label

    def __len__(self):
        return len(self.trails_list)

    def __getitem__(self, idx):
        trail_path = self.trails_list[idx]
        label = self.get_label(trail_path)

        points_path_list = []
        for point_name in sorted(os.listdir(trail_path), key=extract_number_last):
            point_path = os.path.join(trail_path, point_name)
            if os.path.isdir(point_path):
                points_path_list.append(point_path)

        trail_frames_list = []
        trail_lengths = []  # [N1, N2, ..., NT] (T)

        for point_path in points_path_list:
            frames_path_list = []
            for file_name in sorted(os.listdir(point_path), key=extract_number_first):
                if file_name.endswith('.npy'):
                    file_path = os.path.join(point_path, file_name)
                    frames_path_list.append(file_path)

            point_frames_list = []

            for npy_file in frames_path_list:
                data = np.load(npy_file)  # [2, H, W]
                data = complex_to_db_image(data)  # [H, W]
                img = Image.fromarray(data).convert('RGB')  # [H, W, 3]
                img = self.transform(img)  # [3, H, W]
                point_frames_list.append(img)

            point_frames_tensor = torch.stack(point_frames_list, dim=0)  # [N, 3, H, W]
            trail_frames_list.append(point_frames_tensor)
            trail_lengths.append(len(point_frames_list)) 

        label = torch.tensor(label, dtype=torch.long)

        return trail_frames_list, trail_lengths, label

    @staticmethod
    def collate_fn(batch):
        trail_frames_lists, trail_lengths_lists, labels = zip(*batch)

        batch_size = len(trail_frames_lists)

        max_T = max(len(trail_frames_list) for trail_frames_list in trail_frames_lists)
        max_N = max(max(trail_lengths) for trail_lengths in trail_lengths_lists)


        C, H, W = trail_frames_lists[0][0].shape[1], trail_frames_lists[0][0].shape[2], trail_frames_lists[0][0].shape[3]


        padded_trail_frames = torch.zeros(batch_size, max_T, max_N, C, H, W)

        T_lengths = [] 
        n_lengths = [] 

        for i, (trail_frames_list, trail_lengths) in enumerate(zip(trail_frames_lists, trail_lengths_lists)):
            T = len(trail_frames_list)
            T_lengths.append(T)

            sample_n_lengths = trail_lengths + [1] * (max_T - T)
            n_lengths.extend(sample_n_lengths) 

            for t, point_frames in enumerate(trail_frames_list):
                N = point_frames.shape[0]
                padded_trail_frames[i, t, :N] = point_frames  # [B, T, N, C, H, W]

        labels = torch.tensor(labels)

        return padded_trail_frames, T_lengths, n_lengths, labels
