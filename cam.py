import torch
from torchvision import models, transforms
from PIL import Image
import numpy as np
import os
import argparse
from pytorch_grad_cam import Grad<PERSON>M
from pytorch_grad_cam.utils.image import show_cam_on_image
from pytorch_grad_cam.utils.model_targets import ClassifierOutputTarget
import matplotlib.pyplot as plt

from utils import create_model, create_classifier, create_datasets, set_seed

RANDOM_SEED = 233
set_seed(RANDOM_SEED)

class BackboneClassifierWrapper(torch.nn.Module):

    def __init__(self, feature_extractor, classifier):
        super(BackboneClassifierWrapper, self).__init__()
        self.feature_extractor = feature_extractor
        self.classifier = classifier

    def forward(self, x):
        features = self.feature_extractor(x)
        output = self.classifier(features)
        return output


def load_model_and_weights(model_name='resnet', weights_path=None, out_features=512, num_classes=11, device='cpu'):

    feature_extractor = create_model(model_name, out_features=out_features)
    classifier = create_classifier(num_classes=num_classes, in_features=out_features)

    feature_extractor = feature_extractor.to(device)
    classifier = classifier.to(device)

    assert weights_path and os.path.exists(weights_path)

    weights_dict = torch.load(weights_path, map_location=device)
    feature_extractor.load_state_dict(weights_dict['network'], strict=False)
    classifier.load_state_dict(weights_dict['classifier'], strict=False)
    print("Weights loaded successfully!")


    model = BackboneClassifierWrapper(feature_extractor, classifier)
    model.eval()

    return model, feature_extractor


def find_target_layers(feature_extractor):
    target_layers = []

    for name, module in feature_extractor.named_modules():
        if len(list(module.children())) == 0:  #leaf
            print(f"  {name}: {module}")

    # layer4
    for name, module in feature_extractor.named_modules():
        if 'layer4' in name and 'conv' in str(type(module)).lower():
            target_layers.append(module)
            print(f"Found conv layer in layer4: {name}")

    # last-conv
    if not target_layers:
        conv_layers = []
        for name, module in feature_extractor.named_modules():
            if 'conv' in str(type(module)).lower() and '2d' in str(type(module)).lower():
                conv_layers.append((name, module))

        if conv_layers:
            target_layers = [conv_layers[-1][1]]
            print(f"Using last conv layer: {conv_layers[-1][0]}")

    # resnet-layer4
    if not target_layers:
        try:
            from torchvision import models
            temp_resnet = models.resnet18(weights=None)
            target_layers = [temp_resnet.layer4[-1]]
            print("Using standard ResNet18 layer4 as backup")
        except Exception as e:
            print(f"Backup method failed: {e}")
            target_layers = [feature_extractor]

    return target_layers

# img
def apply_colormap_to_grayscale(rgb_img, colormap='viridis', apply_colormap=True):

    if not apply_colormap:
        return rgb_img

    if len(rgb_img.shape) == 3 and np.allclose(rgb_img[:,:,0], rgb_img[:,:,1]) and np.allclose(rgb_img[:,:,1], rgb_img[:,:,2]):
        import matplotlib.cm as cm
        gray_img = rgb_img[:,:,0]

        cmap = getattr(cm, colormap, cm.viridis)
        colored_img = cmap(gray_img)[:,:,:3] 
        return colored_img

    return rgb_img


def preprocess_image(image_path, target_size=(512, 480), colormap='viridis', apply_colormap=True): #[W, H]
    """from img path"""
    preprocess = transforms.Compose([
        transforms.Resize(target_size[::-1]),
        transforms.ToTensor()])

    img = Image.open(image_path).convert("RGB")

    # img_resized = img.resize(target_size)
    rgb_img = np.array(img) / 255.0

    rgb_img = apply_colormap_to_grayscale(rgb_img, colormap, apply_colormap)

    input_tensor = preprocess(img).unsqueeze(0)

    return input_tensor, rgb_img, img


def preprocess_dataset_sample(dataset, sample_idx, target_size=(512, 128)): # [W, H]
    """from dataset"""
    if sample_idx >= len(dataset):
        raise ValueError(f"Sample index {sample_idx} out of range. Dataset has {len(dataset)} samples.")

    image_tensor, label = dataset[sample_idx]

    # tensor - PIL
    if isinstance(image_tensor, torch.Tensor):
        # tensor->numpy  (C, H, W) -> (H, W, C)
        if image_tensor.dim() == 3:
            image_array = image_tensor.permute(1, 2, 0).numpy()
        else:
            image_array = image_tensor.numpy()

        # [0, 1]
        if image_array.max() <= 1.0:
            image_array = (image_array * 255).astype(np.uint8)
        else:
            image_array = image_array.astype(np.uint8)

        # PIL
        img = Image.fromarray(image_array)
    else:
        img = image_tensor

    # RGB
    if img.mode != 'RGB':
        img = img.convert('RGB')

    img_resized = img.resize(target_size)
    rgb_img = np.array(img_resized) / 255.0

    if len(rgb_img.shape) == 3 and np.allclose(rgb_img[:,:,0], rgb_img[:,:,1]) and np.allclose(rgb_img[:,:,1], rgb_img[:,:,2]):
        import matplotlib.cm as cm
        gray_img = rgb_img[:,:,0] 
        colored_img = cm.viridis(gray_img)[:,:,:3]
        rgb_img = colored_img

    input_tensor = image_tensor.unsqueeze(0)

    return input_tensor, rgb_img, img, label


def generate_cam(model, feature_extractor, input_tensor, target_class=None, device='cpu'):

    target_layers = find_target_layers(feature_extractor)
    print(f"Selected target layers: {target_layers}")

    input_tensor = input_tensor.to(device)

    cam = GradCAM(model=model, target_layers=target_layers)

    if target_class is not None:
        targets = [ClassifierOutputTarget(target_class)]
    else:
        targets = None

    grayscale_cam = cam(input_tensor=input_tensor, targets=targets)[0]

    return grayscale_cam


def main():
    parser = argparse.ArgumentParser(description='Generate CAM visualization for ResNet+Classifier')

    parser.add_argument('--use_dataset', action='store_true', help='Use dataset sample instead of image file')
    parser.set_defaults(use_dataset=False)
    parser.add_argument('--dataset_split', type=str, default='test', choices=['train', 'test'], help='Dataset split to use')
    parser.add_argument('--sample_idx', type=int, default=2, help='Dataset sample index to visualize')

    # image
    parser.add_argument('--image_path', type=str, default=r'F:\挑战杯\聚合RD图-zcy\281_Label_3\3_19.png')

    parser.add_argument('--weights_path', type=str, default=r'E:\zcy\cmp_code\weights\resnet-0.9277291745910993.pth')
    parser.add_argument('--model_name', type=str, default='resnet')
    parser.add_argument('--out_features', type=int, default=512)
    parser.add_argument('--num_classes', type=int, default=4)

    # CAM
    parser.add_argument('--target_class', type=int, default=None, help='Target class for CAM (None for highest confidence)')
    parser.add_argument('--device', type=str, default='cuda:0')
    parser.add_argument('--save_path', type=str, default='./img/cam_result.png')

    parser.add_argument('--colormap', type=str, default='viridis',
                       choices=['viridis', 'plasma', 'inferno', 'magma', 'jet', 'hot', 'cool', 'gray'],
                       help='Colormap for grayscale images')
    parser.add_argument('--no_colormap', action='store_true', help='Disable colormap for grayscale images')

    args = parser.parse_args()

    device = torch.device(args.device if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    print("Loading model...")
    model, feature_extractor = load_model_and_weights(
        model_name=args.model_name,
        weights_path=args.weights_path,
        out_features=args.out_features,
        num_classes=args.num_classes,
        device=device
    )

    if args.use_dataset:
        train_set, test_set = create_datasets('RD_dataset_random')

        dataset = train_set if args.dataset_split == 'train' else test_set
        print(f"Using {args.dataset_split} set with {len(dataset)} samples")

        if args.sample_idx >= len(dataset):
            print(f"Error: Sample index {args.sample_idx} out of range. Dataset has {len(dataset)} samples.")
            return

        print(f"Preprocessing dataset sample {args.sample_idx}...")
        input_tensor, rgb_img, _, true_label = preprocess_dataset_sample(dataset, args.sample_idx)
        print(f"True label: {true_label.item()}")

        sample_info = f"dataset_{args.dataset_split}_idx{args.sample_idx}_label{true_label.item()}"
        if args.save_path == 'cam_result.png': 
            args.save_path = f"cam_{sample_info}.png"

    else:
        '''img'''
        if not os.path.exists(args.image_path):
            print(f"Error: Image file '{args.image_path}' not found!")
            return

        print("Preprocessing image file...")
        input_tensor, rgb_img, _ = preprocess_image(args.image_path)
        true_label = None

    with torch.no_grad():
        input_tensor_device = input_tensor.to(device)
        output = model(input_tensor_device)
        probabilities = torch.nn.functional.softmax(output[0], dim=0)
        predicted_class = torch.argmax(probabilities).item()
        confidence = probabilities[predicted_class].item()

    print(f"Predicted class: {predicted_class}, Confidence: {confidence:.4f}")

    grayscale_cam = generate_cam(
        model=model,
        feature_extractor=feature_extractor,
        input_tensor=input_tensor,
        target_class=args.target_class,
        device=device)

    visualization = show_cam_on_image(rgb_img, grayscale_cam, use_rgb=True)

    _, axes = plt.subplots(1, 3, figsize=(15, 5))

    # original
    axes[0].imshow(rgb_img, cmap='viridis')
    axes[0].set_title('Original Image')
    axes[0].axis('off')

    # CAM
    axes[1].imshow(grayscale_cam, cmap='jet')
    axes[1].set_title('CAM Heatmap')
    axes[1].axis('off')

    # original + CAM
    axes[2].imshow(visualization)
    target_class_str = args.target_class if args.target_class is not None else predicted_class

    title = f'CAM Overlay\nPred: {target_class_str}, Conf: {confidence:.3f}'
    if true_label is not None:
        title += f'\nTrue: {true_label.item()}'
        is_correct = predicted_class == true_label.item()
        title += f' {"✓" if is_correct else "✗"}'

    axes[2].set_title(title)
    axes[2].axis('off')

    plt.tight_layout()
    plt.savefig(args.save_path, dpi=300, bbox_inches='tight')
    print(f"CAM result saved to: {args.save_path}")

    if true_label is not None:
        print(f"Sample info: Dataset {args.dataset_split} index {args.sample_idx}")
        print(f"True label: {true_label.item()}")
        print(f"Predicted label: {predicted_class}")
        print(f"Prediction correct: {predicted_class == true_label.item()}")

    plt.show()



if __name__ == "__main__":
    main()
