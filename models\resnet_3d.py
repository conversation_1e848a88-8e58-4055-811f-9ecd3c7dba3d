import torch
import torch.nn as nn
from torchvision.models import resnet18, ResNet18_Weights


class BasicBlock3D(nn.Module):
    expansion = 1

    def __init__(self, inplanes, planes, stride=1, downsample=None):
        super(BasicBlock3D, self).__init__()
        self.conv1 = nn.Conv3d(inplanes, planes, kernel_size=3, stride=stride,
                               padding=1, bias=False)
        self.bn1 = nn.BatchNorm3d(planes)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv3d(planes, planes, kernel_size=3, stride=1,
                               padding=1, bias=False)
        self.bn2 = nn.BatchNorm3d(planes)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        identity = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        out = self.relu(out)

        return out


class ResNet3D(nn.Module):
    def __init__(self, block=BasicBlock3D, layers_num=[2, 2, 2, 2]):
        super(ResNet3D, self).__init__()
        self.out_features = 512
        self.inplanes = 64
        self.conv1 = nn.Conv3d(3, 64, kernel_size=(7, 7, 7), stride=(1, 2, 2),
                               padding=(3, 3, 3), bias=False)
        self.bn1 = nn.BatchNorm3d(64)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool3d(kernel_size=(3, 3, 3), stride=(1, 2, 2), padding=(1, 1, 1))

        self.layer1 = self._make_layer(block, 64, layers_num[0])
        self.layer2 = self._make_layer(block, 128, layers_num[1], stride=2)
        self.layer3 = self._make_layer(block, 256, layers_num[2], stride=2)
        self.layer4 = self._make_layer(block, 512, layers_num[3], stride=2)

        self.avgpool = nn.AdaptiveAvgPool3d((1, 1, 1))

    def _make_layer(self, block, planes, blocks, stride=1):
        downsample = None
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv3d(self.inplanes, planes * block.expansion, kernel_size=1,
                          stride=stride, bias=False),
                nn.BatchNorm3d(planes * block.expansion),
            )

        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample))
        self.inplanes = planes * block.expansion
        for _ in range(1, blocks):
            layers.append(block(self.inplanes, planes))

        return nn.Sequential(*layers)

    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)

        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)

        x = self.avgpool(x)
        x = torch.flatten(x, 1)

        return x


def create_resnet_3d(pretrained=True):

    model = ResNet3D()
    
    if pretrained:
        pretrained_model = resnet18(weights=ResNet18_Weights.IMAGENET1K_V1)
        model = _inflate_weights_2d_to_3d(model, pretrained_model)

    return model


def _inflate_weights_2d_to_3d(model_3d, model_2d):

    state_dict_2d = model_2d.state_dict()
    state_dict_3d = model_3d.state_dict()
    
    for k, v2d in state_dict_2d.items():
        if k in state_dict_3d:
            if 'conv' in k and 'weight' in k:
                if len(v2d.shape) == 4 and len(state_dict_3d[k].shape) == 5:
                    # [out, in, h, w] -> [out, in, t, h, w]
                    temporal_dim = state_dict_3d[k].shape[2]
                    v3d = v2d.unsqueeze(2).repeat(1, 1, temporal_dim, 1, 1)
                    v3d = v3d / temporal_dim 
                    state_dict_3d[k] = v3d
                    print(f"Inflated {k} from {v2d.shape} to {v3d.shape}")

            elif 'downsample' in k and '.0.weight' in k:
                if len(v2d.shape) == 4 and len(state_dict_3d[k].shape) == 5:
                    # [out, in, 1, 1] -> [out, in, 1, 1, 1]
                    v3d = v2d.unsqueeze(2)
                    state_dict_3d[k] = v3d
                    print(f"Inflated downsample {k} from {v2d.shape} to {v3d.shape}")
            elif v2d.shape == state_dict_3d[k].shape:
                # bn
                state_dict_3d[k] = v2d
                print(f"Copied {k} with shape {v2d.shape}")
            else:
                print(f"Skipped {k} due to shape mismatch: {v2d.shape} vs {state_dict_3d[k].shape}")
        else:
            print(f"Skipped {k} due to conv_3d_weight not found")
    
    model_3d.load_state_dict(state_dict_3d, strict=False)
    return model_3d
