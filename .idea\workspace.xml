<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="34016674-f8b8-4a76-97da-aff03c3ca79c" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="30BMWw5oxnKII7z1jbhR4NZj8td" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.Supcon_main.executor": "Run",
    "Python.Visualization.executor": "Debug",
    "Python.cam.executor": "Run",
    "Python.classifer_complex.executor": "Run",
    "Python.eval.executor": "Run",
    "Python.main_train.executor": "Debug",
    "Python.predict_error.executor": "Run",
    "Python.resnet.executor": "Run",
    "Python.run_multiple_backbones.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowPanel.toolwindow.highlight.mappings": "true",
    "WebServerToolWindowPanel.toolwindow.highlight.symlinks": "true",
    "WebServerToolWindowPanel.toolwindow.show.date": "false",
    "WebServerToolWindowPanel.toolwindow.show.permissions": "false",
    "WebServerToolWindowPanel.toolwindow.show.size": "false",
    "last_opened_file_path": "E:/zcy/cmp_code",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "ssh.settings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\zcy\cmp_code\img\RD" />
      <recent name="E:\zcy\cmp_code\img\3_128_334_3d" />
      <recent name="E:\zcy\cmp_code\img\Sup_con" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-PY-251.26927.90" />
        <option value="bundled-python-sdk-41e8cd69c857-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="SshConsoleOptionsProvider">
    <option name="myEncoding" value="UTF-8" />
    <option name="myConnectionType" value="SSH_CONFIG" />
    <option name="myConnectionId" value="Administrator@**************:22 password" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="34016674-f8b8-4a76-97da-aff03c3ca79c" name="更改" comment="" />
      <created>1753096548074</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753096548074</updated>
      <workItem from="1754034612044" duration="528000" />
      <workItem from="1754035152583" duration="106223000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/cmp_code$main_train.coverage" NAME="main_train 覆盖结果" MODIFIED="1754285996777" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/cmp_code$predict_error.coverage" NAME="predict_error 覆盖结果" MODIFIED="1754112394590" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>