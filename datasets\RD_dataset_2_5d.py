import os
import numpy as np
import torch
from PIL import Image
from torch import nn
from torch.utils.data import Dataset
import torchvision.transforms as transforms
import torch.nn.functional as F

def complex_to_db_image(data):
    complex_data = data[0] + 1j * data[1]

    magnitude = np.abs(complex_data) # (H, W)

    eps = 1e-6
    magnitude = 20 * np.log10(magnitude + eps)

    magnitude -= magnitude.min()
    magnitude /= (magnitude.max() + eps)
    magnitude *= 255.0
    magnitude = magnitude.astype(np.uint8)

    return magnitude

class RD_dataset_2_5d(Dataset):
    def __init__(self, folder_list, transform=None):
        # -----------img---------------------------
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize((128, 334)),
                transforms.ToTensor()
            ])
        else:
            self.transform = transform
        # -----------img--------------------------

        self.point_paths = []
        self.labels = []
        self.video_ids = []

        for trail_path in folder_list:
            label = self.get_label(trail_path)
            if label > 3:
                continue
            video_id = self.get_video_id(trail_path)

            for point_name in os.listdir(trail_path):
                point_path = os.path.join(trail_path, point_name)
                if os.path.isdir(point_path):
                    self.point_paths.append(point_path)
                    self.labels.append(label)
                    self.video_ids.append(video_id)

    def get_label(self, trail_path):
        folder_name = os.path.basename(trail_path)
        label = int(folder_name.split('_')[-1]) - 1
        return label

    def get_video_id(self, trail_path):
        folder_name = os.path.basename(trail_path)
        video_id = '_'.join(folder_name.split('_')[:-1])
        return video_id

    def __len__(self):
        return len(self.point_paths)

    def __getitem__(self, idx):
        point_path = self.point_paths[idx]
        label = self.labels[idx]
        video_id = self.video_ids[idx]

        frames_path_list = []
        for file_name in os.listdir(point_path):
            if file_name.endswith('.npy'):
                file_path = os.path.join(point_path, file_name)
                frames_path_list.append(file_path)

        point_frames_list = []

        for npy_file in frames_path_list:
            data = np.load(npy_file)  # [2, H, W]
            data = complex_to_db_image(data)  # [H, W]
            img = Image.fromarray(data).convert('RGB')  # [H, W, 3]
            img = self.transform(img)  # [3, H, W]
            point_frames_list.append(img)

        point_frames_list = torch.stack(point_frames_list, dim=0)  # [N, C, H, W]
        label = torch.tensor(label, dtype=torch.long)

        return point_frames_list, label, video_id

    @staticmethod
    def collate_fn(batch):
        point_frames_lists, labels, video_ids = zip(*batch)
        
        lengths = [len(frames_list) for frames_list in point_frames_lists]

        padded_frames = nn.utils.rnn.pad_sequence(point_frames_lists, batch_first=True)  # [B, max_frames, C, H, W]

        labels = torch.tensor(labels)

        return padded_frames, lengths, labels, video_ids
