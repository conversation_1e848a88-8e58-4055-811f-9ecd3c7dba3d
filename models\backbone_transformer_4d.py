import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.vision_transformer import Block
from timm.layers import trunc_normal_
from models.backbone_lstm_2_5d import Backbone_LSTM_2_5D

class Backbone_Transformer_4D(nn.Module):
    def __init__(self, num_heads=4, depth=3, mlp_ratio=4, drop_rate=0.1, max_seq_len=30, embed_dim=392,
                 lstm_hidden_dim=256, lstm_layers=2, dropout=0.1, name='resnet'):
        super().__init__()

        self.backbone_lstm_2_5d = Backbone_LSTM_2_5D(
            lstm_hidden_dim=lstm_hidden_dim,
            lstm_layers=lstm_layers,
            dropout=dropout,
            name=name
        )

        self.features_dim = self.backbone_lstm_2_5d.out_features
        self.out_features = self.features_dim

        self.proj_to_embed = nn.Linear(self.features_dim, embed_dim)
        self.proj_back = nn.Linear(embed_dim, self.features_dim)
        self.pos_embed = nn.Parameter(torch.zeros(1, max_seq_len, embed_dim))  # (1, max_seq_len, embed_dim)
        self.num_heads = num_heads

        self.blocks = nn.ModuleList([
            Block(dim=embed_dim,
                  num_heads=num_heads,
                  mlp_ratio=mlp_ratio,
                  qkv_bias=True,
                  drop_path=0.1,
                  attn_drop=drop_rate,
                  norm_layer=nn.LayerNorm
            ) for _ in range(depth)])
        self.norm = nn.LayerNorm(embed_dim)

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def make_attn_mask(self, seq_len, T_lengths, device):
        B = len(T_lengths)

        casual_bool = torch.triu(torch.ones(seq_len, seq_len, device=device, dtype=torch.bool), diagonal=1)
        casual_mask = casual_bool.float().masked_fill(casual_bool, float('-inf'))
        casual_mask = casual_mask.unsqueeze(0).expand(B, seq_len, seq_len)

        pad_bool = torch.arange(seq_len, device=device).unsqueeze(0) >= torch.tensor(T_lengths, device=device).unsqueeze(1)
        pad_mask = pad_bool.unsqueeze(1).expand(B, seq_len, seq_len)
        pad_mask = pad_mask.float().masked_fill(pad_mask, float('-inf'))

        mask = casual_mask + pad_mask
        mask = mask.unsqueeze(1).expand(B, self.num_heads, seq_len, seq_len)
        return mask



    def forward(self, x, T_lengths, n_lengths, return_intermediate=False):

        B, T_max, N_max, C, H, W = x.shape

        x_reshaped = x.view(B*T_max, N_max, C, H, W)

        features_flat = self.backbone_lstm_2_5d(x_reshaped, n_lengths)  # (B*T_max, features_dim)

        features = features_flat.view(B, T_max, self.features_dim)

        features_first = features[:, 0, :]  # (B, features_dim)

        intermediate_features = features.clone() if return_intermediate else None

        features = self.proj_to_embed(features)  # (B, T_max, embed_dim)

        # PE
        max_len = self.pos_embed.size(1)
        if T_max <= max_len:
            pos = self.pos_embed[:, :T_max, :]
        else:
            pos = F.interpolate(
                self.pos_embed.transpose(1, 2),
                size=T_max,
                mode='linear',
                align_corners=False
            ).transpose(1, 2)
        features = features + pos

        attn_mask = self.make_attn_mask(T_max, T_lengths, x.device)  # (B, num_heads, T_max, T_max)

        for blk in self.blocks:
            features = blk(features, attn_mask=attn_mask)
        # (B, T, embed_dim)
        features = self.norm(features)

        features = self.proj_back(features)  # (B, T_max, out_features)

        features[:, 0, :] = features_first

        if return_intermediate:
            return features, intermediate_features
        else:
            return features


def create_backbone_transformer_4d(**kwargs):
    return Backbone_Transformer_4D(**kwargs)
