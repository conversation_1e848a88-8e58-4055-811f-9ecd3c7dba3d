import os

os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"

# os.environ["MASTER_ADDR"] = "218.194.40.34"
# os.environ["MASTER_ADDR"] = "218.194.35.167"
os.environ["MASTER_PORT"] = "29500"

import math
import argparse
from functools import partial
from colorama import Fore
import numpy as np

import torch
import torch.optim as optim
import torch.optim.lr_scheduler as lr_scheduler
from matplotlib import pyplot as plt
from torch.utils.data import DataLoader
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data.distributed import DistributedSampler

from datasets.RD_dataset_3d import RD_dataset_3d
from datasets.RD_dataset_4d import RD_dataset_4d
from datasets.RD_dataset_2_5d import RD_dataset_2_5d
from datasets.RD_dataset_5d import RD_dataset_5d
from utils import create_model, train_one_epoch_2d, evaluate_2d, create_datasets, create_classifier, set_seed,\
     train_one_epoch_3d, evaluate_3d, train_one_epoch_4d, evaluate_4d, create_confusion_matrix, evaluate_3d_test,\
     evaluate_4d_test, train_one_epoch_2_5d, evaluate_2_5d, train_one_epoch_5d, evaluate_5d


def check_ddp_gradient_sync(model, rank, world_size, epoch=None, step=None):
    """check ddp gradient sync"""
    if not dist.is_initialized() or world_size <= 1:
        return

    grad_norms = []
    param_norms = []

    for name, param in model.named_parameters():
        if param.requires_grad:
            # data norm
            param_norm = param.data.norm().item()
            param_norms.append(param_norm)

            # gradient norm
            if param.grad is not None:
                grad_norm = param.grad.data.norm().item()
                grad_norms.append(grad_norm)

    # total
    total_grad_norm = sum(grad_norms) if grad_norms else 0.0
    total_param_norm = sum(param_norms) if param_norms else 0.0

    local_info = torch.tensor([total_grad_norm, total_param_norm], dtype=torch.float32, device=f'cuda:{rank}')
    gathered_info = [torch.zeros_like(local_info) for _ in range(world_size)]
    dist.all_gather(gathered_info, local_info)

    if rank == 0:
        grad_norms_all = [info[0].item() for info in gathered_info]
        param_norms_all = [info[1].item() for info in gathered_info]

        grad_std = torch.tensor(grad_norms_all).std().item()
        param_std = torch.tensor(param_norms_all).std().item()

        prefix = f"[Epoch {epoch}, Step {step}]" if epoch is not None and step is not None else "[Sync Check]"

        if grad_std < 1e-6:
            print(f"{prefix}  梯度同步正常 (std: {grad_std:.2e})")
        else:
            print(f"{prefix}  梯度可能未同步 (std: {grad_std:.2e})")
            print(f"  各进程梯度范数: {[f'{norm:.4f}' for norm in grad_norms_all]}")

        if param_std < 1e-6:
            print(f"{prefix}  参数同步正常 (std: {param_std:.2e})")
        else:
            print(f"{prefix}  参数可能未同步 (std: {param_std:.2e})")
            print(f"  各进程参数范数: {[f'{norm:.4f}' for norm in param_norms_all]}")

RANDOM_SEED = 233
set_seed(RANDOM_SEED)


def setup_ddp():
    dist.init_process_group("gloo")

    rank = int(os.environ["RANK"])
    local_rank = int(os.environ["LOCAL_RANK"])
    world_size = int(os.environ["WORLD_SIZE"])

    torch.cuda.set_device(local_rank)
    print(f"[Rank {rank}] DDP initialized on {local_rank}, world_size={world_size}")

    return rank, local_rank, world_size


def cleanup_ddp(): 
    dist.destroy_process_group()



def main(args):

    pretrain_flag = False # is use pretrain weights

    dataset_name = args.dataset_name
    dataset_type = dataset_name.split('_')[2]

    use_ddp = dataset_type in ['4d', '5d'] and "RANK" in os.environ

    if use_ddp:
        rank, local_rank, world_size = setup_ddp()
        device = f'cuda:{local_rank}'

        seed_offset = rank * 1000
        set_seed(RANDOM_SEED + seed_offset)

        if rank == 0:
            print(f'{dataset_type.upper()} dataset use DDP train, {world_size} GPUs')
            print(f'Rank {rank}: Using device {device}')
            print(f'Random seed: {RANDOM_SEED + seed_offset} (base: {RANDOM_SEED}, offset: {seed_offset})')
    else:
        rank = 0
        world_size = 1
        device = args.device
        print('Single GPU mode, device:', device)
        print(f'Random seed: {RANDOM_SEED}')

    batch_size = args.batch_size
    if not use_ddp or rank == 0:
        print('Batch size:', batch_size)

    if os.path.exists("./weights") is False:
        os.makedirs("./weights")

    dataset_name = args.dataset_name
    dataset_type = dataset_name.split('_')[2]

    if args.root != '':
        root = args.root
        root_type = root.split('/')[-1]
    else:
        root = None
        root_type = None

    datasets_list = create_datasets(dataset_name, root=root, k_fold=args.k_fold)

    collate_fn = None

    if dataset_type == '4d':
        if not use_ddp or rank == 0:
            print('4D dataset')
        train_one_epoch = partial(train_one_epoch_4d, alpha=args.alpha)
        evaluate = evaluate_4d
        collate_fn = RD_dataset_4d.collate_fn
        model_name = 'backbone_transformer_4d'
    elif dataset_type == '5d':
        if not use_ddp or rank == 0:
            print('5D dataset')
        train_one_epoch = partial(train_one_epoch_5d, alpha=args.alpha)
        evaluate = evaluate_5d
        collate_fn = RD_dataset_5d.collate_fn
        model_name = 'rd_point_fusion_5d'
    elif dataset_type == '3d':
        print('3D dataset')
        train_one_epoch = train_one_epoch_3d
        evaluate = evaluate_3d
        collate_fn = RD_dataset_3d.collate_fn
        model_name = 'backbone_transformer'
    elif dataset_type == '2d':
        print('2D dataset')
        train_one_epoch = train_one_epoch_2d
        evaluate = evaluate_2d
        model_name = 'backbone'
    elif dataset_type == '2.5d':
        print('2.5D dataset')
        train_one_epoch = train_one_epoch_2_5d
        evaluate = evaluate_2_5d
        collate_fn = RD_dataset_2_5d.collate_fn
        model_name = 'backbone_lstm_2_5d'
    else:
        raise ValueError(f"Unsupported dataset type: {dataset_type}")

    for train_set, test_set, fold in datasets_list:
        if not use_ddp or rank == 0:
            print(f'Fold {fold}: train_set={len(train_set)}, test_set={len(test_set)}')
            print('Fold:', fold)

        # data loader
        if use_ddp:
            # DistributedSampler seed
            train_sampler = DistributedSampler(train_set, num_replicas=world_size, rank=rank, shuffle=True, seed=RANDOM_SEED)
            test_sampler = DistributedSampler(test_set, num_replicas=world_size, rank=rank, shuffle=False, seed=RANDOM_SEED)

            train_loader = DataLoader(train_set, batch_size=batch_size, sampler=train_sampler,
                                    num_workers=2, collate_fn=collate_fn, pin_memory=True)
            test_loader = DataLoader(test_set, batch_size=batch_size, sampler=test_sampler,
                                   num_workers=2, collate_fn=collate_fn, pin_memory=True)
        else:
            train_loader = DataLoader(train_set, shuffle=True, batch_size=batch_size, num_workers=0, collate_fn=collate_fn)
            test_loader = DataLoader(test_set, shuffle=True, batch_size=batch_size, num_workers=0, collate_fn=collate_fn)


        model = create_model(model_name, name=args.backbone_name)
        out_features = model.out_features
        classifier = create_classifier(num_classes=args.num_classes, in_features=out_features)

        if not use_ddp or rank == 0:
            print("Model created:", model_name, args.backbone_name)
            print('num_classes:', args.num_classes)

            n_parameters = sum(p.numel() for p in model.parameters() if p.requires_grad)
            print('number of params (M): %.2f' % (n_parameters / 1.e6))

        classifier = classifier.to(device)
        model = model.to(device)

        # DDP wrap model
        if use_ddp:
            model = DDP(model, device_ids=[rank], output_device=rank, find_unused_parameters=True)
            classifier = DDP(classifier, device_ids=[rank], output_device=rank)

        if args.weights != "":
            weights_dict = torch.load(args.weights, map_location=device, weights_only=True)
            actual_model = model.module if use_ddp else model
            actual_classifier = classifier.module if use_ddp else classifier
            print(actual_model.load_state_dict(weights_dict['network'], strict=False))
            print(actual_classifier.load_state_dict(weights_dict['classifier'], strict=False))

        if args.pre_weights != "":
            pre_weights = os.path.join(args.pre_weights, f"Sup-{dataset_type}-{args.backbone_name}-{fold}.pth")
            assert os.path.exists(pre_weights), "pre_weights file: '{}' not exist.".format(pre_weights)
            weights_dict = torch.load(pre_weights, map_location=device, weights_only=True)
            actual_model = model.module if use_ddp else model
            print(actual_model.load_state_dict(weights_dict, strict=False))
        
        if args.pre_weights_backbone != "":
            pre_weights_backbone = os.path.join(args.pre_weights_backbone, f"Sup-{root_type}-2d-{fold}-{args.add_noise}-{args.add_crop}.pth")
            print(pre_weights_backbone)
            pretrain_flag = True
            assert os.path.exists(pre_weights_backbone), "pre_weights_backbone file: '{}' not exist.".format(pre_weights_backbone)
            weights_dict = torch.load(pre_weights_backbone, map_location=device, weights_only=True)
            actual_model = model.module if use_ddp else model
            print(actual_model.backbone_lstm_2_5d.backbone.load_state_dict(weights_dict, strict=False))

        if args.pre_weights_backbone_lstm != "":
            pre_weights_backbone_lstm = os.path.join(args.pre_weights_backbone_lstm, f"{root_type}-2.5d-{fold}.pth")
            print(pre_weights_backbone_lstm)
            pretrain_flag = True
            assert os.path.exists(pre_weights_backbone_lstm), "pre_weights_backbone_lstm file: '{}' not exist.".format(pre_weights_backbone_lstm)
            weights_dict = torch.load(pre_weights_backbone_lstm, map_location=device, weights_only=True)
            actual_model = model.module if use_ddp else model
            print(actual_model.backbone_lstm_2_5d.load_state_dict(weights_dict['network'], strict=False))

        if args.freeze_layers:
            if not use_ddp or rank == 0:
                print("Freeze all layers in the model")
            for name, para in model.named_parameters():
                para.requires_grad_(False)

        pg = [p for p in model.parameters() if p.requires_grad]
        optimizer = optim.SGD(pg+ list(classifier.parameters()), lr=args.lr, momentum=0.9)

        is_warmup = False
        if args.warmup_epochs > 0:
            if not use_ddp or rank == 0:
                print('start warmup training, warmup epochs:', args.warmup_epochs)
            is_warmup = True
            def get_lr_lambda(epochs, warmup_batches):
                def lr_lambda(step):
                    # warmup
                    if step < warmup_batches:
                        return (step + 1) / warmup_batches
                    else:
                        epoch = step // (len(train_loader))
                        return pow(0.9,epoch)

                return lr_lambda

            warmup_epochs = args.warmup_epochs
            warmup_batches = len(train_loader) * warmup_epochs
            scheduler = lr_scheduler.LambdaLR(optimizer, lr_lambda=get_lr_lambda(args.epochs, warmup_batches))

        else:
            if not use_ddp or rank == 0:
                print('off warmup training')
            lf = lambda x: ((1 + math.cos(x * math.pi / args.epochs)) / 2) * (1 - args.lrf) + args.lrf  # cosine
            scheduler = lr_scheduler.LambdaLR(optimizer, lr_lambda=lf)
        if not use_ddp or rank == 0:
            print('epochs:', args.epochs)

        best_frame_acc = 0
        best_video_acc = 0
        frame_acc_list = []
        video_acc_list = []
        for epoch in range(args.epochs):
            if use_ddp:
                train_sampler.set_epoch(epoch)

            # train
            train_one_epoch(model=model,
                            classifier=classifier,
                            optimizer=optimizer,
                            data_loader=train_loader,
                            device=device,
                            epoch=epoch,
                            scheduler=scheduler,
                            warmup=is_warmup)

            # check ddp sync
            if use_ddp:
                check_ddp_gradient_sync(model, rank, world_size, epoch)

            # validate
            val_acc, all_labels, all_preds = evaluate(model=model,
                                                    data_loader=test_loader,
                                                    device=device,
                                                    epoch=epoch,
                                                    classifier=classifier)
            frame_acc, video_acc = val_acc
            frame_acc_list.append(frame_acc)
            val_acc = frame_acc
            if video_acc != -1:
                video_acc_list.append(video_acc)
                val_acc = video_acc
                

            if frame_acc > best_frame_acc:
                best_frame_acc = frame_acc
                if not use_ddp or rank == 0:  
                    print(Fore.WHITE + "Best Frame Acc:{:.2f}".format(best_frame_acc*100))
                    if epoch >=args.save_epoch:
                        create_confusion_matrix(epoch, all_labels, all_preds, args.num_classes, fold, root_type, dataset_type)
                        model_state_dict = model.module.state_dict() if use_ddp else model.state_dict()
                        classifier_state_dict = classifier.module.state_dict() if use_ddp else classifier.state_dict()
                        torch.save({"network": model_state_dict,
                                "classifier": classifier_state_dict},
                                "./weights/{}-{}-{}.pth".format(root_type, dataset_type, fold))

            if video_acc > best_video_acc:
                best_video_acc = video_acc
                if not use_ddp or rank == 0:
                    print(Fore.WHITE + "Best Video Acc:{:.2f}".format(best_video_acc*100))

        if not use_ddp or rank == 0:
            print("Best Frame Acc: {:.2f}, Best Video Acc: {:.2f}".format(best_frame_acc*100, best_video_acc*100))

        if not use_ddp or rank == 0:
            plt.figure(figsize=(8, 5))
            plt.plot(range(1,args.epochs+1), frame_acc_list, marker='o', label='Frame Accuracy')
            plt.xticks(range(1, args.epochs+1, 5))
            plt.xlabel('Epoch')
            plt.ylabel('Accuracy')
            plt.ylim(0.6,0.95)
            plt.legend()
            # plt.show()
            if pretrain_flag:
                plt.savefig(f'E:\zcy\cmp_code\img/{fold}_{args.add_noise}_{args.add_crop}_frame_{root_type}_{dataset_type}_{best_frame_acc*100:.2f}.png')
            else:
                plt.savefig(f'E:\zcy\cmp_code\img/{fold}_frame_{root_type}_{dataset_type}_{best_frame_acc*100:.2f}.png')
            plt.close()

            if video_acc_list:
                plt.figure(figsize=(8, 5))
                plt.plot(range(1,args.epochs+1), video_acc_list, marker='o', label='Video Accuracy')
                plt.xticks(range(1, args.epochs+1, 5))
                plt.xlabel('Epoch')
                plt.ylabel('Accuracy')
                plt.ylim(0.4,0.93)
                plt.legend()
                if pretrain_flag:
                    plt.savefig(f'E:\zcy\cmp_code\img/{fold}_{args.add_noise}_{args.add_crop}_video_{root_type}_{dataset_type}_{best_video_acc*100:.2f}.png')
                else:
                    plt.savefig(f'E:\zcy\cmp_code\img/{fold}_video_{root_type}_{dataset_type}_{best_video_acc*100:.2f}.png')
                plt.close()

        if use_ddp:
            dist.barrier()

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--num_classes', type=int, default=4)
    parser.add_argument('--epochs', type=int, default=10)
    parser.add_argument('--batch_size', type=int, default=8)
    parser.add_argument('--lr', type=float, default=0.01)
    parser.add_argument('--lrf', type=float, default=0.01)
    parser.add_argument('--save_epoch', type=int, default=10, help='when to save model')
    parser.add_argument('--warmup_epochs', type=int, default=0, help='epochs to warmup LR')

    parser.add_argument('--backbone_name', default='resnet', help='create backbone name')
    parser.add_argument('--dataset_name', default='RD_dataset_5d', help='RD_dataset, RD_dataset_3d, RD_dataset_4d, RD_dataset_2.5d, RD_dataset_5d')
    parser.add_argument('--root', default='./data/SNR_RD_npy', help='root path')

    parser.add_argument('--weights', type=str, default=r"", help='initial weights path')
    parser.add_argument('--pre_weights', type=str, default=r"",
                        help='initial pretrain weights path')
    parser.add_argument('--pre_weights_backbone', type=str, default=r"",
                        help='initial pretrain weights of backbone')

    parser.add_argument('--pre_weights_backbone_lstm', type=str, default=r"",
                        help='initial pretrain weights of backbone_lstm_2_5d')

    parser.add_argument('--freeze_layers', action='store_true', default=False)
    parser.add_argument('--device', default='cuda:0')
    parser.add_argument('--k_fold', nargs='+', type=int, default=[0,1,2,3,4])

    parser.add_argument('--add_noise', action='store_true', default=False)
    parser.add_argument('--add_crop', action='store_true', default=False)
    parser.add_argument('--alpha', type=float, default=0.0, help='weight for intermediate loss')

    opt = parser.parse_args()

    try:
        main(opt)
    finally:
        if "RANK" in os.environ:
            cleanup_ddp()
    