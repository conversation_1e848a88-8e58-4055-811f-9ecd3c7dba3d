import argparse
import numpy as np
import os
import random

import torch
import torchvision.transforms as transforms
from torch.utils.data import DataLoader

import timm
assert timm.__version__ == "0.3.2"  # version !!!!
import timm.optim.optim_factory as optim_factory

from utils import pretrain_mae, create_datasets, create_model, set_seed


RANDOM_SEED = 233
set_seed(RANDOM_SEED)

def main(args):

    device = torch.device(args.device)
    print("device: ", device)
    batch_size = args.batch_size
    print("batch_size: ", batch_size)

    if not os.path.exists("./weights"):
        os.makedirs("./weights")

    # transform_train = transforms.Compose([
    #         transforms.RandomResizedCrop(args.input_size, scale=(0.2, 1.0), interpolation=transforms.InterpolationMode.BICUBIC),
    #         transforms.RandomHorizontalFlip(),
    #         transforms.ToTensor(),
    #         transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])])
    train_set, test_set = create_datasets(args.dataset_name)
    print('train set size:', len(train_set))


    train_loader = DataLoader(train_set,
                              shuffle=True,
                              batch_size=args.batch_size,
                              num_workers=args.num_workers,
                              pin_memory=args.pin_mem,
                              drop_last=True)

    model = create_model(args.model_name, norm_pix_loss=args.norm_pix_loss, img_size=args.input_size) # base, large, huge
    print("Model created:", args.model_name)
    print('normalization pixel loss:', args.norm_pix_loss)

    model.to(device)

    eff_batch_size = args.batch_size * args.accum_iter
    
    if args.lr is None:  # only base_lr is specified
        args.lr = args.blr * eff_batch_size / 256

    print("base lr: %.2e" % (args.lr * 256 / eff_batch_size))
    print("actual lr: %.2e" % args.lr)

    print("accumulate grad iterations: %d" % args.accum_iter)
    print("effective batch size: %d" % eff_batch_size)
    
    # set wd as 0 for bias and norm layers
    param_groups = optim_factory.add_weight_decay(model, args.weight_decay)
    optimizer = torch.optim.AdamW(param_groups, lr=args.lr, betas=(0.9, 0.95)) # type: ignore

    print(f"warmup epochs: {args.warmup_epochs}")
    best_loss = float('inf')
    for epoch in range(args.epochs):

        loss = pretrain_mae(model=model,
                            train_loader=train_loader,
                            optimizer=optimizer,
                            device=device,
                            epoch=epoch,
                            args=args)

        if loss <= best_loss:
            best_loss = loss
            print(f"Best loss: {best_loss:.4f}")
            torch.save(model.state_dict(), f"./weights/{args.model_name}.pth")

    print(f"Training completed. Best Loss: {best_loss:.4f}")

if __name__ == '__main__':
    parser = argparse.ArgumentParser('MAE pre-training', add_help=False)

    # train
    parser.add_argument('--batch_size', default=64, type=int)
    parser.add_argument('--epochs', default=400, type=int)
    parser.add_argument('--accum_iter', default=1, type=int,
                        help='Accumulate gradient iterations (for increasing the effective batch size under memory constraints)')
    parser.add_argument('--warmup_epochs', type=int, default=40, help='epochs to warmup LR')

    # model
    parser.add_argument('--model_name', default='mae_vit_base_patch16', type=str,
                        help='mae_vit_base_patch16, mae_vit_large_patch16, mae_vit_huge_patch14')

    parser.add_argument('--input_size', default=224, type=int, help='images input size')
    parser.add_argument('--mask_ratio', default=0.75, type=float, help='Masking ratio (percentage of removed patches).')
    parser.add_argument('--norm_pix_loss', action='store_true', help='Use (per-patch) normalized pixels as targets for computing loss')
    parser.set_defaults(norm_pix_loss=False)

    # Optimizer
    parser.add_argument('--weight_decay', type=float, default=0.05, help='weight decay (default: 0.05)')
    parser.add_argument('--lr', type=float, default=None, help='learning rate (absolute lr)')
    parser.add_argument('--blr', type=float, default=1e-3,
                        help='base learning rate: absolute_lr = base_lr * total_batch_size / 256')
    parser.add_argument('--min_lr', type=float, default=0., metavar='LR',
                        help='lower lr bound for cyclic schedulers that hit 0')

    # dataset
    parser.add_argument('--dataset_name', default='RD_dataset_3d', type=str, help='create datasets name')
    parser.add_argument('--device', default='cuda:0')
    parser.add_argument('--num_workers', default=10, type=int)
    parser.add_argument('--pin_mem', action='store_true',
                        help='Pin CPU memory in DataLoader for more efficient (sometimes) transfer to GPU.')
    parser.add_argument('--no_pin_mem', action='store_false', dest='pin_mem')
    parser.set_defaults(pin_mem=True)

    args = parser.parse_args()
    main(args)