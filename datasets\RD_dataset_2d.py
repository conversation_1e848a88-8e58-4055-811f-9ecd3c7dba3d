import os
import torch
import torchvision.transforms as transforms
from PIL import Image
from torch.utils.data import Dataset
import scipy.io as sio
import numpy as np
from datasets.RD_dataset_3d import complex_to_abs_angle, complex_to_db_image
import torch.nn.functional as F
from scipy.ndimage import gaussian_filter

def complex_to_augment_abs_angle(data, row, column):
    complex_data = data[0] + 1j * data[1]
    magnitude = np.abs(complex_data)

    # impulse = np.zeros((9, 9));
    # impulse[4, 4] = 1
    # k99 = gaussian_filter(impulse, sigma=1, mode='constant', cval=0)
    #
    # magnitude[row-4:row+5, column-4:column+5] = magnitude[row-4:row+5, column-4:column+5] + (k99 + 1) * np.max(magnitude)

    angle = np.angle(complex_data)
    data = np.stack([magnitude, angle], axis=0) # [2, H, W]
    
    # if magnitude.ndim == 3:
    #     data = magnitude[None, :, :, :]
    # else:
    #     data = magnitude[None, :, :]
    return data

# RD_zcy
class RD_dataset_2d(Dataset):
    def __init__(self, folder_list, transform=None, pretrain=False):
        #-------------img----------------------
        shape = (128,334)
        print(shape)
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize(shape), #(31, 334)
                transforms.ToTensor()
            ])
        else:
            self.transform = transform
        #-------------img----------------------
        self.pretrain = pretrain
        self.file_paths = []
        self.labels = []
        self.video_ids = []
        for folder_path in folder_list:
            label = self.get_label(folder_path)
            if label > 3 and not pretrain:
                continue
            # for file_name in os.listdir(folder_path):
            #     if file_name.endswith('.npy'):
            #         file_path = os.path.join(folder_path, file_name)
            #         self.file_paths.append(file_path)
            #         self.labels.append(label)
            #         self.video_ids.append(folder_path)
            for point_folder in os.listdir(folder_path):
                point_folder_path = os.path.join(folder_path, point_folder)
                for file_name in os.listdir(point_folder_path):
                    if file_name.endswith('.npy'):
                        file_path = os.path.join(point_folder_path, file_name)
                        self.file_paths.append(file_path)
                        self.labels.append(label)
                        self.video_ids.append(folder_path)

    def get_label(self, folder_path):
        folder_name = os.path.basename(folder_path)
        label = int(folder_name.split('_')[-1]) - 1
        return label

    def __len__(self):
        return len(self.file_paths)

    def __getitem__(self, idx):
        file_path = self.file_paths[idx]
        data = np.load(file_path)  #[2, H, W]

        # ----------------mask--------------------
        # file_name = os.path.basename(file_path)
        # row = int(file_name.split('_')[2])
        # column = int(file_name.split('_')[3].split('.')[0])
        # data = complex_to_augment_abs_angle(data, row, column)
        #-----------------mask---------------------

        # ---------------img---------------------
        magnitude = complex_to_db_image(data) #[H, W]
        # crop
        if self.pretrain:
            file_name = os.path.basename(file_path)
            row = int(file_name.split('_')[1])
            column = int(float(file_name.split('_')[2]))

            magnitude_crop = np.zeros_like(magnitude)

            H, W = magnitude.shape
            row_start = max(0, row - 3)
            row_end = min(H, row + 4)

            col_start = max(0, column - 20)
            col_end = min(W, column + 21)

            magnitude_crop[row_start:row_end, :] = magnitude[row_start:row_end, :]
            magnitude_crop[:, col_start:col_end] = magnitude[:, col_start:col_end]

            data_crop = Image.fromarray(magnitude_crop).convert('RGB')
            data_crop = self.transform(data_crop)



        data = Image.fromarray(magnitude).convert('RGB')
        # ----------------img---------------------

        # -----------------mag-angle------------------
        # data = complex_to_abs_angle(data)
        # -----------------mag-angle------------------

        if self.transform:
            data = self.transform(data)
        else:
            data = torch.tensor(data).float()
        # ---------------mag-angle------------------
        # data = data.unsqueeze(0)
        # data = F.interpolate(data, size=(128, 512), mode='bilinear', align_corners=False).squeeze(0)
        # ---------------mag-angle------------------
        label = torch.tensor(self.labels[idx], dtype=torch.long)
        video_id = self.video_ids[idx]

        if self.pretrain:
            return data, data_crop, label, video_id
        else:
            return data, label, video_id
