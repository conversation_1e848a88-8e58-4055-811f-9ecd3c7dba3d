import torch
import torch.nn as nn
import torch.nn.functional as F
from models.backbone import create_backbone

class Backbone_LSTM_2_5D(nn.Module):
    def __init__(self, lstm_hidden_dim=256, lstm_layers=2, dropout=0.1, name='resnet'):
        super().__init__()

        self.backbone = create_backbone(name=name)
        self.features_dim = self.backbone.out_features
        self.out_features = self.features_dim

        self.lstm = nn.LSTM(
            input_size=self.features_dim, 
            hidden_size=lstm_hidden_dim, 
            num_layers=lstm_layers,
            batch_first=True, 
            bidirectional=True,
            dropout=dropout if lstm_layers > 1 else 0
        )
        
        self.lstm_proj = nn.Linear(lstm_hidden_dim * 2, self.features_dim)
        
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            nn.init.trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LSTM):
            for name, param in m.named_parameters():
                if 'weight_ih' in name:
                    nn.init.xavier_uniform_(param.data)
                elif 'weight_hh' in name:
                    nn.init.orthogonal_(param.data)
                elif 'bias' in name:
                    nn.init.constant_(param.data, 0)

    def forward(self, x, lengths):
        B, max_frames, C, H, W = x.shape

        x = x.contiguous().view(B * max_frames, C, H, W)

        backbone_features = self.backbone(x)  # (B*max_frames, features_dim)
        backbone_features = backbone_features.view(B, max_frames, -1)  # (B, max_frames, features_dim)

        packed_sequences = nn.utils.rnn.pack_padded_sequence(
            backbone_features, lengths, batch_first=True, enforce_sorted=False
        )

        packed_lstm_out, _ = self.lstm(packed_sequences)

        lstm_out, _ = nn.utils.rnn.pad_packed_sequence(packed_lstm_out, batch_first=True)  # (B, max_seq_len, lstm_hidden_dim*2)

        lstm_out = self.lstm_proj(lstm_out)  # (B, max_seq_len, features_dim)

        B = len(lengths)
        max_seq_len = lstm_out.size(1)

        lengths_tensor = torch.tensor(lengths, device=lstm_out.device)
        seq_mask = torch.arange(max_seq_len, device=lstm_out.device).unsqueeze(0) < lengths_tensor.unsqueeze(1)  # (B, max_seq_len)

        single_frame_mask = (lengths_tensor == 1)  # (B,)

        masked_lstm_out = lstm_out * seq_mask.unsqueeze(-1)  # (B, max_seq_len, features_dim)
        summed_features = masked_lstm_out.sum(dim=1)  # (B, features_dim)
        mean_features = summed_features / lengths_tensor.unsqueeze(-1).float()  # (B, features_dim)

        single_frame_features = backbone_features[:, 0, :]  # (B, features_dim)

        point_features = torch.where(
            single_frame_mask.unsqueeze(-1),
            single_frame_features,
            mean_features
        )  # (B, features_dim)

        return point_features




def create_backbone_lstm_2_5d(**kwargs):
    return Backbone_LSTM_2_5D(**kwargs)
