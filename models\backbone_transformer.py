import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.vision_transformer import Block
from timm.layers import trunc_normal_
from models.backbone import create_backbone
from models.range_sequence_transformer import create_range_sequence_transformer_small

class Backbone_Transformer(nn.Module):
    def __init__(self, num_heads=8, depth=6, mlp_ratio=4, drop_rate=0.1, max_seq_len=30, embed_dim=392, name='resnet'):
        super().__init__()
        
        self.backbone = create_backbone(name=name)
        self.features_dim = self.backbone.out_features
        self.out_features = self.features_dim
        # self.backbone  = create_range_sequence_transformer_small()

        self.proj_to_embed = nn.Linear(self.features_dim, embed_dim)
        self.proj_back = nn.Linear(embed_dim, self.features_dim)
        self.pos_embed = nn.Parameter(torch.zeros(1, max_seq_len, embed_dim))  # (1, max_seq_len, embed_dim)
        self.num_heads = num_heads

        self.blocks = nn.ModuleList([
            Block(dim=embed_dim,
                  num_heads=num_heads,
                  mlp_ratio=mlp_ratio,
                  qkv_bias=True,
                  drop_path=0.1,
                  attn_drop=drop_rate,
                  norm_layer=nn.LayerNorm
            ) for _ in range(depth)])
        self.norm = nn.LayerNorm(embed_dim)

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def make_attn_mask(self, seq_len, lengths, device):

        B = len(lengths)

        casual_bool = torch.triu(torch.ones(seq_len, seq_len, device=device, dtype=torch.bool), diagonal=1)                                   # (seq_len, seq_len)
        casual_mask = casual_bool.float().masked_fill(casual_bool, float('-inf')) # [seq_len, seq_len]
        casual_mask = casual_mask.unsqueeze(0).expand(B, seq_len, seq_len)

        pad_bool = torch.arange(seq_len, device=device).unsqueeze(0) >= torch.tensor(lengths, device=device).unsqueeze(1)      # (B, seq_len)
        pad_mask = pad_bool.unsqueeze(1).expand(B, seq_len, seq_len)    
        pad_mask = pad_mask.float().masked_fill(pad_mask, float('-inf'))

        mask = casual_mask + pad_mask                                   
        mask = mask.unsqueeze(1).expand(B, self.num_heads, seq_len, seq_len)
        return mask

    def forward(self, x, lengths):
 
        B, C, T, H, W = x.shape
        x = x.permute(0, 2, 1, 3, 4)   # (B, T, C, H, W)
        x = x.contiguous().view(B*T, C, H, W)

        features = self.backbone(x).view(B, T, -1)
        features_first = features[:, 0, :] #(B, features_dim)

        features = self.proj_to_embed(features)       # (B, T, embed_dim)

        # PE
        max_len = self.pos_embed.size(1)
        if T <= max_len:
            pos = self.pos_embed[:, :T, :]        
        else:
            pos = F.interpolate(
                self.pos_embed.transpose(1, 2),    
                size=T,                            
                mode='linear',
                align_corners=False
            ).transpose(1, 2)           
        features = features + pos

        attn_mask = self.make_attn_mask(T, lengths, x.device) #(B, num_heads, T, T)

        for blk in self.blocks:
            features = blk(features, attn_mask=attn_mask)
        # (B, T, embed_dim)
        features = self.norm(features)

        features = self.proj_back(features)  #(B, T, out_features)
        features[:, 0, :] = features_first
        return features


def create_backbone_transformer(**kwargs):

    return Backbone_Transformer(**kwargs)