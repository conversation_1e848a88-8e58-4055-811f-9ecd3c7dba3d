import torch
import torch.nn as nn
from torchvision import models
from PIL import Image
import numpy as np
import matplotlib.cm as cm
from sklearn.decomposition import PCA
from utils import create_datasets, set_seed

RANDOM_SEED = 233
set_seed(RANDOM_SEED)



class ResNetFirst(nn.Module):
    def __init__(self, pretrained=True):
        super().__init__()
        model = models.resnet18(weights=models.ResNet18_Weights.DEFAULT if pretrained else None)
        self.model = nn.Sequential(*list(model.children())[:5])

    def forward(self, x):
        return self.model(x)


if __name__ == '__main__':
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    net = ResNetFirst(pretrained=False)
    weights_dict = torch.load(r"E:\zcy\cmp_code\weights\resnet-0.9277291745910993.pth", map_location=device, weights_only=True)
    
    net.load_state_dict(weights_dict['network'], strict= False)
    net = net.to(device)
    net.eval()

    # train_set, test_set = create_datasets('RD_dataset')
    # x, label = test_set[0]
    img_root = r'F:\挑战杯\聚合RD图-zcy\281_Label_3\3_19.png'
    img = Image.open(img_root)
    x = img.convert('RGB')
    x = np.array(x) / 255.0
    x = x.transpose(2, 0, 1)
    x = torch.from_numpy(x).float().to(device)
    x = x.unsqueeze(0)

    with torch.no_grad():
        feat = net(x)                  
    feat_np = feat[0].cpu().numpy()

    h, w = feat_np.shape[1:]
    flat = feat_np.transpose(1,2,0).reshape(-1, 64) 
    pca = PCA(n_components=3, whiten=False)
    rgb = pca.fit_transform(flat).reshape(h, w, 3)

    rgb -= rgb.min()
    rgb /= rgb.max()
    rgb = (rgb * 255).astype(np.uint8)

    out_img = Image.fromarray(rgb)
    out_img.save('./img/visualization.png')
    print("Visualization saved to visualization.png")
