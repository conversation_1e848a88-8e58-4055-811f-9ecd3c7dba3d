from torch import nn
from models.complexLayers import ComplexLinear,ComplexBatchNorm1d
from models.complexFunctions import complex_relu

class MLPHead_complex(nn.Module):
    def __init__(self, in_channels, mlp_hidden_size, projection_size):
        super(MLPHead_complex, self).__init__()

        self.linear1 = ComplexLinear(in_channels, mlp_hidden_size)
        self.bn1 = ComplexBatchNorm1d(mlp_hidden_size)
        self.linear2=ComplexLinear(mlp_hidden_size, projection_size)

    def forward(self, x):
        x=self.linear1(x)
        x=self.bn1(x)
        x=complex_relu(x)
        x=self.linear2(x)
        return x