import torch
import torch.nn as nn
import torchvision.models as models
from models.backbone import create_backbone


class Backbone_LSTM(nn.Module):
    def __init__(self, hidden_dim=256, num_layers=2, name='resnet'):
        super(Backbone_LSTM, self).__init__()

        self.out_features = hidden_dim
        self.backbone = create_backbone(name=name)
        feature_dim = self.backbone.out_features

        self.lstm = nn.LSTM(
            input_size=feature_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            bidirectional=False
        )

    def forward(self, x, lengths):
        
        B, C, T, H, W = x.shape
        x = x.permute(0, 2, 1, 3, 4)   # (B, T, C, H, W)
        x = x.contiguous().view(B*T, C, H, W)

        features = self.backbone(x)
        features = features.view(B, T, -1) # (B, T, D1)

        packed = nn.utils.rnn.pack_padded_sequence(features, lengths, batch_first=True, enforce_sorted=False)

        packed_out, _ = self.lstm(packed)
        lstm_out, _= nn.utils.rnn.pad_packed_sequence(packed_out, batch_first=True) # (B, T, D2)

        return lstm_out

def create_backbone_lstm(hidden_dim=256, num_layers=2, name='resnet'):

    return Backbone_LSTM(hidden_dim=hidden_dim, num_layers=num_layers, name=name)