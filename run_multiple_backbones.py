import subprocess
import sys

def main():
    backbone_list = [
        "efficientnetv2",
        "swin",
        "efficientnetv1",
        "mobilenetv3",
        "convnext",
        "mobilenetv4"
    ]
    
    common_args = [
        "--epochs", "20",
        "--batch_size", "8",
        "--lr", "0.01",
        "--num_classes", "4",
        "--dataset_name", "RD_dataset_3d",
        "--device", "cuda:0"
    ]
    
    for backbone_name in backbone_list:
        cmd = [sys.executable, "main_train.py", "--backbone_name", backbone_name]
        cmd.extend(common_args)
        subprocess.run(cmd, check=True, capture_output=False)

    # file_list = [
    #     "Supcon_main.py",
    #     "main_train.py",
    # ]
    #
    # for file in file_list:
    #     cmd = [sys.executable, file]
    #     subprocess.run(cmd, check=True, capture_output=False)
        

if __name__ == "__main__":
    
    main()

