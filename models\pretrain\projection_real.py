from models.pretrain.mlp_head_real import MLPHead_real
import torch.nn as nn
import torch.nn.functional as F
class projection_real(nn.Module):
    def __init__(self, in_channels, mlp_hidden_size, projection_size):
        super(projection_real, self).__init__()
        self.projection = MLPHead_real(in_channels, mlp_hidden_size, projection_size)

    def forward(self, inputs):
        features = self.projection(inputs)
        features = F.normalize(features, dim=-1)
        return features