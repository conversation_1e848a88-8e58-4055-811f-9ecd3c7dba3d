import os
import math
import argparse
import random

from colorama import Fore
import numpy as np

import torch
import torch.optim as optim
import torch.optim.lr_scheduler as lr_scheduler
from torch.utils.data import DataLoader

from datasets.RD_dataset_3d import RD_dataset_3d
from utils import create_model, create_datasets, create_prediction, create_projection, pretrain_Supcon_2d, \
pretrain_Supcon_3d, set_seed

from models.pretrain.Sup_loss import SupConLoss

RANDOM_SEED = 233
set_seed(RANDOM_SEED)


def main(args):
    device = args.device
    print('device:', device)

    if os.path.exists("./weights") is False:
        os.makedirs("./weights")

    batch_size = args.batch_size
    print('Batch size:', batch_size)

    if args.root != '':
        root = args.root
        root_type = root.split('/')[-1]
    else:
        root = None
        root_type = None
    dataset_name = args.dataset_name
    dataset_type = dataset_name.split('_')[2]

    datasets_list = create_datasets(dataset_name, root=root, pretrain=True, k_fold=args.k_fold)


    collate_fn = None

    if dataset_type == '3d':
        print('3D dataset')
        pretrain_Supcon = pretrain_Supcon_3d
        collate_fn = RD_dataset_3d.collate_fn
        model_name = 'backbone_transformer'
    elif dataset_type == '2d':
        pretrain_Supcon = pretrain_Supcon_2d
        model_name = 'backbone'

    for train_set, test_set, fold in datasets_list:
        print(f'Fold {fold}: train_set={len(train_set)}, test_set={len(test_set)}')

        train_loader = DataLoader(train_set, 
                                  shuffle=True, 
                                  batch_size=batch_size, 
                                  num_workers=0, 
                                  drop_last=True, 
                                  collate_fn=collate_fn)

        model = create_model(model_name, name=args.backbone_name)
        out_features = model.out_features
        projection = create_projection(model_type='real', in_channels=out_features)
        print("Model created:", model_name)


        criterion = SupConLoss().to(device)
        model = model.to(device)
        projection = projection.to(device)


        pg = [p for p in model.parameters() if p.requires_grad]
        optimizer = optim.SGD(pg + list(projection.parameters()) + list(criterion.parameters()), lr=args.lr, momentum=0.9)

        is_warmup = False
        if args.warmup_epochs > 0:
            is_warmup = True
            print('start warmup training, warmup epochs:', args.warmup_epochs)
            def get_lr_lambda(epochs, warmup_batches):
                def lr_lambda(step):
                    # warmup
                    if step < warmup_batches:
                        return (step + 1) / warmup_batches
                    else:
                        epoch = step // (len(train_loader))
                        return pow(0.9, epoch)

                return lr_lambda

            warmup_epochs = args.warmup_epochs
            warmup_batches = len(train_loader) * warmup_epochs
            scheduler = lr_scheduler.LambdaLR(optimizer, lr_lambda=get_lr_lambda(args.epochs, warmup_batches))
        else:
            print('off warmup training')
            lf = lambda x: ((1 + math.cos(x * math.pi / args.epochs)) / 2) * (1 - args.lrf) + args.lrf  # cosine
            scheduler = lr_scheduler.LambdaLR(optimizer, lr_lambda=lf)
        print('epochs:', args.epochs)

        best_loss = float('inf')
        for epoch in range(args.epochs):
            # pretrain
            loss = pretrain_Supcon(encoder=model,
                                  projection=projection,
                                  criterion=criterion,
                                  optimizer=optimizer,
                                  epoch=epoch,
                                  batch_size=batch_size,
                                  max_epoch=args.epochs,
                                  train_loader=train_loader,
                                  device=device,
                                  scheduler=scheduler,
                                  warm_up=is_warmup,
                                  img_noise=args.add_noise,
                                  img_crop=args.add_crop)

            if loss <= best_loss:
                best_loss = loss
                print(Fore.WHITE + "Best loss:{:.4f}".format(best_loss))
                if epoch >= args.save_epoch:
                    torch.save(model.state_dict(), f"./weights/Sup-{root_type}-{dataset_type}-{fold}-{args.add_noise}-{args.add_crop}.pth")

        print("Best Loss: {:.4f}".format(best_loss))


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--epochs', type=int, default=20)
    parser.add_argument('--batch_size', type=int, default=128)
    parser.add_argument('--lr', type=float, default=0.01)
    parser.add_argument('--lrf', type=float, default=0.01)
    parser.add_argument('--save_epoch', type=int, default=1, help='when to save model')
    parser.add_argument('--warmup_epochs', type=int, default=2, help='epochs to warmup LR')

    parser.add_argument('--dataset_name', default='RD_dataset_2d', help='RD_dataset, RD_dataset_3d, RD_dataset_random, RD_dataset_3d_random')
    parser.add_argument('--backbone_name', default='resnet', help='create backbone name')
    parser.add_argument('--root', default='./data/SNR_RD_npy', help='root path')

    parser.add_argument('--k_fold', nargs='+', type=int, default=[0,1,2,3,4])

    parser.add_argument('--add_noise', action='store_true', default=False)
    parser.add_argument('--add_crop', action='store_true', default=False)

    parser.add_argument('--device', default='cuda:1', help='device id (i.e. 0 osr 0,1 or cpu)')
    opt = parser.parse_args()

    main(opt)



