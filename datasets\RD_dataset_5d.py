'''
point track RD_dataset_5d
'''

import os
import numpy as np
import torch
from PIL import Image
from torch import nn
from torch.utils.data import Dataset
import torchvision.transforms as transforms
import torch.nn.functional as F
import glob

def complex_to_db_image(data):
    complex_data = data[0] + 1j * data[1]

    magnitude = np.abs(complex_data) # (H, W)

    eps = 1e-6
    magnitude = 20 * np.log10(magnitude + eps)

    magnitude -= magnitude.min()
    magnitude /= (magnitude.max() + eps)
    magnitude *= 255.0
    magnitude = magnitude.astype(np.uint8)

    return magnitude

def extract_number_first(file_name):
    return int(file_name.split('_')[0])

def extract_number_last(file_name):
    return int(file_name.split('_')[-1].split('.')[0])

class RD_dataset_5d(Dataset):
    def __init__(self, folder_list, transform=None):
        self.trails_list = folder_list
        # -----------img---------------------------
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize((128, 334)),
                transforms.ToTensor()
            ])
        else:
            self.transform = transform
        # -----------img--------------------------

    def get_label(self, trail_path):
        folder_name = os.path.basename(trail_path)
        label = int(folder_name.split('_')[-1]) - 1
        return label

    def __len__(self):
        return len(self.trails_list)
    
    def point_data_get(self,path):
        with open(path) as point_f:
            point_data=point_f.readlines()[1:]
        point_data = ''.join(point_data)
        point_data = point_data.replace('[', '').replace(']', '').replace(',', ' ').split()
        point_data=np.reshape(np.array(point_data),(-1,9))
        point_data=point_data[:,2:]
        point_data = list(map(float, list(np.reshape(point_data,(-1)))))
        point_data=np.reshape(np.array(point_data),(-1,7))
        point_data = torch.tensor(point_data,dtype=torch.float32)
        return point_data

    def track_data_get(self,path):
        with open(path) as track_f:
            track_data = track_f.readlines()[1:]
        track_data = ''.join(track_data)
        track_data = track_data.replace('[', '').replace(']', '').replace(',', ' ').split()
        track_data = np.reshape(np.array(track_data), (-1, 10))
        track_data = track_data[:, 2:]
        track_data = list(map(float, list(np.reshape(track_data, (-1)))))
        track_data = np.reshape(np.array(track_data), (-1, 8))
        track_data = torch.tensor(track_data,dtype=torch.float32)
        return track_data

    def get_point_path(self,trail_path, label):
        root_path =r'F:\挑战杯\1384-CQ-08中国航天科工二院二十三所-低空监视雷达目标智能识别技术研究数据集\点迹'
        rd_folder_name = os.path.basename(trail_path)
        batch_idx = int(rd_folder_name.split('_')[0])
        files = glob.glob(os.path.join(root_path, f'PointTracks_{batch_idx}_{label+1}_*.txt'))
        point_path = files[0]
        return point_path
    
    def get_track_path(self,trail_path, label):
        root_path =r'F:\挑战杯\1384-CQ-08中国航天科工二院二十三所-低空监视雷达目标智能识别技术研究数据集\航迹'
        rd_folder_name = os.path.basename(trail_path)
        batch_idx = int(rd_folder_name.split('_')[0])
        files = glob.glob(os.path.join(root_path,f'Tracks_{batch_idx}_{label+1}_*.txt'))
        track_path = files[0]
        return track_path

    def __getitem__(self, idx):
        trail_path = self.trails_list[idx]
        label = self.get_label(trail_path)
        
        # -----point-track----
        point_path = self.get_point_path(trail_path, label)
        track_path = self.get_track_path(trail_path, label)
        point_data = self.point_data_get(point_path)
        track_data = self.track_data_get(track_path)
        track_data=torch.diff(track_data,dim=0)
        point_data=torch.diff(point_data,dim=0)
        
        point_data=torch.nn.functional.pad(point_data,[0,0,1,0],mode="constant",value=0)
        track_data=torch.nn.functional.pad(track_data,[0,0,1,0],mode="constant",value=0)
        # -----point-track----

        points_path_list = []
        for point_name in sorted(os.listdir(trail_path), key=extract_number_last):
            point_path = os.path.join(trail_path, point_name)
            if os.path.isdir(point_path):
                points_path_list.append(point_path)

        trail_frames_list = []
        trail_lengths = []  # [N1, N2, ..., NT] (T)

        for point_path in points_path_list:
            frames_path_list = []
            for file_name in sorted(os.listdir(point_path), key=extract_number_first):
                if file_name.endswith('.npy'):
                    file_path = os.path.join(point_path, file_name)
                    frames_path_list.append(file_path)

            point_frames_list = []

            for npy_file in frames_path_list:
                data = np.load(npy_file)  # [2, H, W]
                data = complex_to_db_image(data)  # [H, W]
                img = Image.fromarray(data).convert('RGB')  # [H, W, 3]
                img = self.transform(img)  # [3, H, W]
                point_frames_list.append(img)

            point_frames_tensor = torch.stack(point_frames_list, dim=0)  # [N, 3, H, W]
            trail_frames_list.append(point_frames_tensor)
            trail_lengths.append(len(point_frames_list)) 

        label = torch.tensor(label, dtype=torch.long)

        return trail_frames_list, trail_lengths, label, point_data, track_data

    @staticmethod
    def collate_fn(batch):
        trail_frames_lists, trail_lengths_lists, labels, point_data_lists, track_data_lists = zip(*batch)

        batch_size = len(trail_frames_lists)

        max_T = max(len(trail_frames_list) for trail_frames_list in trail_frames_lists)
        max_N = max(max(trail_lengths) for trail_lengths in trail_lengths_lists)


        C, H, W = trail_frames_lists[0][0].shape[1], trail_frames_lists[0][0].shape[2], trail_frames_lists[0][0].shape[3]


        padded_trail_frames = torch.zeros(batch_size, max_T, max_N, C, H, W)
        padded_point_data = torch.zeros(batch_size, max_T, 7)
        padded_track_data = torch.zeros(batch_size, max_T, 8)

        T_lengths = [] 
        n_lengths = [] 

        for i, (trail_frames_list, trail_lengths, point_data, track_data) in enumerate(zip(trail_frames_lists, trail_lengths_lists, point_data_lists, track_data_lists)):
            T = len(trail_frames_list)
            T_lengths.append(T)

            sample_n_lengths = trail_lengths + [1] * (max_T - T)
            n_lengths.extend(sample_n_lengths) 

            for t, point_frames in enumerate(trail_frames_list):
                N = point_frames.shape[0]
                padded_trail_frames[i, t, :N] = point_frames  # [B, T, N, C, H, W]
            
            assert point_data.shape[0] == track_data.shape[0] and point_data.shape[0] == T, f'not match T'
            padded_point_data[i, :T] = point_data
            padded_track_data[i, :T] = track_data

        labels = torch.tensor(labels)

        return padded_trail_frames, T_lengths, n_lengths, labels, padded_point_data, padded_track_data
