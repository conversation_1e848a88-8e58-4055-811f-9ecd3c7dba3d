import torch
import torch.nn as nn
import torchvision.models as models
import timm
from torchvision.models import vit_b_16, ViT_B_16_Weights, convnext_tiny, Swin_T_Weights, swin_t
# from transformers import CLIPVisionModel

class Backbone(nn.Module):
    def __init__(self, name=None, pretrained=True):
        super(Backbone, self).__init__()

        if pretrained:
            if name == 'resnet':
                model = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)
                self.out_features = 512
            elif name == 'efficientnetv2':
                model = models.efficientnet_v2_s(weights=models.EfficientNet_V2_S_Weights.IMAGENET1K_V1) #1280
                self.out_features = 1280
            elif name == 'swin':
                model = swin_t(weights=Swin_T_Weights.IMAGENET1K_V1) #768
                self.out_features = 768
            elif name == 'efficientnetv1':
                model = models.efficientnet_b1(weights=models.EfficientNet_B1_Weights.DEFAULT)
                self.out_features = 1280
            elif name == 'mobilenetv3':
                model = models.mobilenet_v3_small(weights=models.MobileNet_V3_Small_Weights.IMAGENET1K_V1) #576
                self.out_features = 576
            elif name == 'convnext':
                model = convnext_tiny(weights=models.ConvNeXt_Tiny_Weights.IMAGENET1K_V1) #768
                self.out_features = 768
            elif name == 'mobilenetv4':
                model = timm.create_model('mobilenetv4_conv_medium.e500_r256_in1k', pretrained=True)
                self.out_features = 1280
            elif name == 'convnextv2':
                model = timm.create_model('convnextv2_pico.fcmae_ft_in1k', pretrained=True, num_classes=0)
                self.out_features = 512
            # model = swin_t(weights=Swin_T_Weights.IMAGENET1K_V1) #768
            # model = vit_b_16(weights=models.ViT_B_16_Weights.IMAGENET1K_V1)
            # model = convnext_tiny(weights=models.ConvNeXt_Tiny_Weights.IMAGENET1K_V1) #768
            # model = models.efficientnet_b1(weights=models.EfficientNet_B1_Weights.DEFAULT)
            # model = models.mobilenet_v3_large(weights=models.MobileNet_V3_Large_Weights.IMAGENET1K_V2) #960
            # model = models.RegNet_Y_800MF_Weights(weights=models.RegNet_Y_800MF_Weights.IMAGENET1K_V2)
        else:
            model = models.resnet18(weights=None)

        if name !='convnextv2':
            self.model = nn.Sequential(*list(model.children())[:-1])
        else:
            self.model = model
        # model = timm.create_model('vit_tiny_patch16_224', num_classes=0, img_size=(128,512), patch_size=7)
        # self.model = timm.create_model('efficientnet_b0', pretrained=True, num_classes=0)
        # self.model = timm.create_model('efficientnetv2_m', pretrained=True, num_classes=0)
        # Clip vit
        # self.model = CLIPVisionModel.from_pretrained("openai/clip-vit-base-patch32",ignore_mismatched_sizes=False)

    def forward(self, x):

        x = self.model(x)
        x = x.view(x.size(0), -1)

        return x



def create_backbone(pretrained=True, name='resnet'):

    return Backbone(pretrained=pretrained, name=name)
