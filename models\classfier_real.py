from torch import nn

class classifier_real(nn.Module):
    def __init__(self, in_features=1024, num_classes=11):
        super(classifier_real, self).__init__()
        self.linear = nn.Linear(in_features,num_classes)
        # self.threshold = 100.

    def forward(self,x):
        # # ReAct
        # if not self.training:
        #     x = x.clip(max=self.threshold)
        x = self.linear(x)
        return x